{"logs": [{"outputFile": "com.wendy.face.app-mergeDebugResources-56:/values-en-rXC/values-en-rXC.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,243", "endColumns": "187,186", "endOffsets": "238,425"}, "to": {"startLines": "113,114", "startColumns": "4,4", "startOffsets": "22593,22781", "endColumns": "187,186", "endOffsets": "22776,22963"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,310,510,719,904,1106,1321,1494,1671,1862,2055,2253,2449,2652,2847,3044,3239,3432,3623,3807,4011,4216,4417,4624,4826,5031,5303,5503", "endColumns": "204,199,208,184,201,214,172,176,190,192,197,195,202,194,196,194,192,190,183,203,204,200,206,201,204,271,199,178", "endOffsets": "305,505,714,899,1101,1316,1489,1666,1857,2050,2248,2444,2647,2842,3039,3234,3427,3618,3802,4006,4211,4412,4619,4821,5026,5298,5498,5677"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,104", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,310,510,719,904,1106,1321,1494,1671,1862,2055,2253,2449,2652,2847,3044,3239,3432,3623,3807,4011,4216,4417,4624,4826,5031,5303,20936", "endColumns": "204,199,208,184,201,214,172,176,190,192,197,195,202,194,196,194,192,190,183,203,204,200,206,201,204,271,199,178", "endOffsets": "305,505,714,899,1101,1316,1489,1666,1857,2050,2248,2444,2647,2842,3039,3234,3427,3618,3802,4006,4211,4412,4619,4821,5026,5298,5498,21110"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,275,492,706,923,1124,1322,1532,1771,1989,2228,2414,2614,2808,3008,3229,3457,3664,3894,4120,4349,4610,4832,5051,5272,5497,5692,5892,6110,6336,6535,6738,6943,7173,7414,7623,7824,8003,8201,8397,8604,8794,8984,9189,9371,9557,9761,9965,10167,10369,10559,10768,10972,11179,11398,11581,11782", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,206,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "270,487,701,918,1119,1317,1527,1766,1984,2223,2409,2609,2803,3003,3224,3452,3659,3889,4115,4344,4605,4827,5046,5267,5492,5687,5887,6105,6331,6530,6733,6938,7168,7409,7618,7819,7998,8196,8392,8599,8789,8979,9184,9366,9552,9756,9960,10162,10364,10554,10763,10967,11174,11393,11576,11777,11975"}, "to": {"startLines": "43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "8270,8490,8707,8921,9138,9339,9537,9747,9986,10204,10443,10629,10829,11023,11223,11444,11672,11879,12109,12335,12564,12825,13047,13266,13487,13712,13907,14107,14325,14551,14750,14953,15158,15388,15629,15838,16039,16218,16416,16612,16819,17009,17199,17404,17586,17772,17976,18180,18382,18584,18774,18983,19187,19394,19613,19796,19997", "endColumns": "219,216,213,216,200,197,209,238,217,238,185,199,193,199,220,227,206,229,225,228,260,221,218,220,224,194,199,217,225,198,202,204,229,240,208,200,178,197,195,206,189,189,204,181,185,203,203,201,201,189,208,203,206,218,182,200,197", "endOffsets": "8485,8702,8916,9133,9334,9532,9742,9981,10199,10438,10624,10824,11018,11218,11439,11667,11874,12104,12330,12559,12820,13042,13261,13482,13707,13902,14102,14320,14546,14745,14948,15153,15383,15624,15833,16034,16213,16411,16607,16814,17004,17194,17399,17581,17767,17971,18175,18377,18579,18769,18978,19182,19389,19608,19791,19992,20190"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,251,456,657,858,1065,1270,1482", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "246,451,652,853,1060,1265,1477,1681"}, "to": {"startLines": "29,30,31,32,33,34,35,109", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "5503,5699,5904,6105,6306,6513,6718,21805", "endColumns": "195,204,200,200,206,204,211,203", "endOffsets": "5694,5899,6100,6301,6508,6713,6925,22004"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-en-rXC\\values-en-rXC.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,296,481,678,880,1067,1252,1445,1633,1820,2001,2186,2352,2530,2705,2876,3055,3222", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "291,476,673,875,1062,1247,1440,1628,1815,1996,2181,2347,2525,2700,2871,3050,3217,3455"}, "to": {"startLines": "36,37,38,39,40,41,42,100,101,102,103,105,106,107,108,110,111,112", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6930,7121,7306,7503,7705,7892,8077,20195,20383,20570,20751,21115,21281,21459,21634,22009,22188,22355", "endColumns": "190,184,196,201,186,184,192,187,186,180,184,165,177,174,170,178,166,237", "endOffsets": "7116,7301,7498,7700,7887,8072,8265,20378,20565,20746,20931,21276,21454,21629,21800,22183,22350,22588"}}]}]}