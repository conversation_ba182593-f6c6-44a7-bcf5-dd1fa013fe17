1<?xml version="1.0" encoding="utf-8"?>
2<manifest xmlns:android="http://schemas.android.com/apk/res/android"
3    package="com.wendy.face"
4    android:versionCode="1"
5    android:versionName="1.0" >
6
7    <uses-sdk
8        android:minSdkVersion="28"
9        android:targetSdkVersion="35" />
10
11    <uses-feature android:name="android.hardware.camera.any" />
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:5-64
11-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:5:19-61
12
13    <uses-permission android:name="android.permission.CAMERA" />
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:5-65
13-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:6:22-62
14    <uses-permission
14-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:5-8:38
15        android:name="android.permission.WRITE_EXTERNAL_STORAGE"
15-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:7:22-78
16        android:maxSdkVersion="28" />
16-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:8:9-35
17    <uses-permission android:name="android.permission.READ_EXTERNAL_STORAGE" />
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:5-80
17-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:9:22-77
18
19    <queries>
19-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:22:5-26:15
20        <intent>
20-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:23:9-25:18
21            <action android:name="androidx.camera.extensions.action.VENDOR_ACTION" />
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:13-86
21-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:24:21-83
22        </intent>
23    </queries>
24
25    <permission
25-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:22:5-24:47
26        android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION"
26-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:23:9-81
27        android:protectionLevel="signature" />
27-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:24:9-44
28
29    <uses-permission android:name="com.wendy.face.DYNAMIC_RECEIVER_NOT_EXPORTED_PERMISSION" /> <!-- Although the *SdkVersion is captured in gradle build files, this is required for non gradle builds -->
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:5-97
29-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:26:22-94
30    <!-- <uses-sdk android:minSdkVersion="14"/> -->
31    <uses-permission android:name="android.permission.ACCESS_NETWORK_STATE" />
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:5-79
31-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:25:22-76
32    <uses-permission android:name="android.permission.INTERNET" />
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:5-67
32-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:26:22-64
33
34    <application
34-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:11:5-38:19
35        android:allowBackup="true"
35-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:12:9-35
36        android:appComponentFactory="androidx.core.app.CoreComponentFactory"
36-->[androidx.core:core:1.13.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\afde12078f7f3fef585f13cd9d4f1674\transformed\core-1.13.1\AndroidManifest.xml:28:18-86
37        android:dataExtractionRules="@xml/data_extraction_rules"
37-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:13:9-65
38        android:debuggable="true"
39        android:extractNativeLibs="false"
40        android:fullBackupContent="@xml/backup_rules"
40-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:14:9-54
41        android:icon="@mipmap/ic_launcher"
41-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:15:9-43
42        android:label="@string/app_name"
42-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:16:9-41
43        android:roundIcon="@mipmap/ic_launcher_round"
43-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:17:9-54
44        android:supportsRtl="true"
44-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:18:9-35
45        android:theme="@style/Theme.Face" >
45-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:19:9-42
46        <activity
46-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:21:9-31:20
47            android:name="com.wendy.face.MainActivity"
47-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:22:13-41
48            android:exported="true"
48-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:23:13-36
49            android:label="@string/app_name"
49-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:24:13-45
50            android:theme="@style/Theme.Face.FullScreen" >
50-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:25:13-57
51            <intent-filter>
51-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:26:13-30:29
52                <action android:name="android.intent.action.MAIN" />
52-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:17-69
52-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:27:25-66
53
54                <category android:name="android.intent.category.LAUNCHER" />
54-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:17-77
54-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:29:27-74
55            </intent-filter>
56        </activity>
57        <activity
57-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:32:9-37:20
58            android:name="com.wendy.face.TestCameraActivity"
58-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:33:13-47
59            android:exported="false"
59-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:34:13-37
60            android:label="Test Camera"
60-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:35:13-40
61            android:theme="@style/Theme.Face" >
61-->D:\workspace\gitee.com\wendy\face\app\src\main\AndroidManifest.xml:36:13-46
62        </activity>
63
64        <uses-library
64-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:29:9-31:40
65            android:name="androidx.camera.extensions.impl"
65-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:30:13-59
66            android:required="false" />
66-->[androidx.camera:camera-extensions:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\7174d9c888ae98529a7d8745a19334b9\transformed\camera-extensions-1.3.1\AndroidManifest.xml:31:13-37
67
68        <service
68-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:24:9-33:19
69            android:name="androidx.camera.core.impl.MetadataHolderService"
69-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:25:13-75
70            android:enabled="false"
70-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:26:13-36
71            android:exported="false" >
71-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:27:13-37
72            <meta-data
72-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:30:13-32:89
73                android:name="androidx.camera.core.impl.MetadataHolderService.DEFAULT_CONFIG_PROVIDER"
73-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:31:17-103
74                android:value="androidx.camera.camera2.Camera2Config$DefaultProvider" />
74-->[androidx.camera:camera-camera2:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\39959a6f52757fe9191c79dc0e003492\transformed\camera-camera2-1.3.1\AndroidManifest.xml:32:17-86
75        </service>
76
77        <activity
77-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:23:9-25:39
78            android:name="androidx.compose.ui.tooling.PreviewActivity"
78-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:24:13-71
79            android:exported="true" />
79-->[androidx.compose.ui:ui-tooling-android:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\c9736fdf0fb2243f2547120df2dfc721\transformed\ui-tooling-release\AndroidManifest.xml:25:13-36
80        <activity
80-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:23:9-25:39
81            android:name="androidx.activity.ComponentActivity"
81-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:24:13-63
82            android:exported="true" />
82-->[androidx.compose.ui:ui-test-manifest:1.7.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\29891f8b28b8cddc69a28c96172dde33\transformed\ui-test-manifest-1.7.0\AndroidManifest.xml:25:13-36
83
84        <service
84-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:8:9-14:19
85            android:name="com.google.mlkit.common.internal.MlKitComponentDiscoveryService"
85-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:9:13-91
86            android:directBootAware="true"
86-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:17:13-43
87            android:exported="false" >
87-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:10:13-37
88            <meta-data
88-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:11:13-13:85
89                android:name="com.google.firebase.components:com.google.mlkit.vision.facemesh.internal.FaceMeshRegistrar"
89-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:12:17-122
90                android:value="com.google.firebase.components.ComponentRegistrar" />
90-->[com.google.mlkit:face-mesh-detection:16.0.0-beta1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\AndroidManifest.xml:13:17-82
91            <meta-data
91-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76de22ab496890d5bb43b2576e725184\transformed\vision-common-17.2.1\AndroidManifest.xml:12:13-14:85
92                android:name="com.google.firebase.components:com.google.mlkit.vision.common.internal.VisionCommonRegistrar"
92-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76de22ab496890d5bb43b2576e725184\transformed\vision-common-17.2.1\AndroidManifest.xml:13:17-124
93                android:value="com.google.firebase.components.ComponentRegistrar" />
93-->[com.google.mlkit:vision-common:17.2.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\76de22ab496890d5bb43b2576e725184\transformed\vision-common-17.2.1\AndroidManifest.xml:14:17-82
94            <meta-data
94-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:20:13-22:85
95                android:name="com.google.firebase.components:com.google.mlkit.common.internal.CommonComponentRegistrar"
95-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:21:17-120
96                android:value="com.google.firebase.components.ComponentRegistrar" />
96-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:22:17-82
97        </service>
98
99        <provider
99-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:9:9-13:38
100            android:name="com.google.mlkit.common.internal.MlKitInitProvider"
100-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:10:13-78
101            android:authorities="com.wendy.face.mlkitinitprovider"
101-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:11:13-69
102            android:exported="false"
102-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:12:13-37
103            android:initOrder="99" />
103-->[com.google.mlkit:common:18.5.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\d3807ea9f437cfe3285561f0177ff9ed\transformed\common-18.5.0\AndroidManifest.xml:13:13-35
104
105        <activity
105-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:9-22:45
106            android:name="com.google.android.gms.common.api.GoogleApiActivity"
106-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:20:19-85
107            android:exported="false"
107-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:22:19-43
108            android:theme="@android:style/Theme.Translucent.NoTitleBar" />
108-->[com.google.android.gms:play-services-base:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\b463a313bba75631a42e21f95a59b107\transformed\play-services-base-18.1.0\AndroidManifest.xml:21:19-78
109
110        <meta-data
110-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:21:9-23:69
111            android:name="com.google.android.gms.version"
111-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:22:13-58
112            android:value="@integer/google_play_services_version" />
112-->[com.google.android.gms:play-services-basement:18.1.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\bd665940b510ccfb87ca6845c9679e75\transformed\play-services-basement-18.1.0\AndroidManifest.xml:23:13-66
113
114        <provider
114-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:24:9-32:20
115            android:name="androidx.startup.InitializationProvider"
115-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:25:13-67
116            android:authorities="com.wendy.face.androidx-startup"
116-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:26:13-68
117            android:exported="false" >
117-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:27:13-37
118            <meta-data
118-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:29:13-31:52
119                android:name="androidx.emoji2.text.EmojiCompatInitializer"
119-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:30:17-75
120                android:value="androidx.startup" />
120-->[androidx.emoji2:emoji2:1.3.0] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\f9caa547508a012762f421c3c63e36d4\transformed\emoji2-1.3.0\AndroidManifest.xml:31:17-49
121            <meta-data
121-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:29:13-31:52
122                android:name="androidx.lifecycle.ProcessLifecycleInitializer"
122-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:30:17-78
123                android:value="androidx.startup" />
123-->[androidx.lifecycle:lifecycle-process:2.8.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\59ce49f538141d4c650699bebc97fc75\transformed\lifecycle-process-2.8.3\AndroidManifest.xml:31:17-49
124            <meta-data
124-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:29:13-31:52
125                android:name="androidx.profileinstaller.ProfileInstallerInitializer"
125-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:30:17-85
126                android:value="androidx.startup" />
126-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:31:17-49
127        </provider>
128
129        <service
129-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:29:9-35:19
130            android:name="com.google.android.datatransport.runtime.backends.TransportBackendDiscovery"
130-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:30:13-103
131            android:exported="false" >
131-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:31:13-37
132            <meta-data
132-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:32:13-34:39
133                android:name="backend:com.google.android.datatransport.cct.CctBackendFactory"
133-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:33:17-94
134                android:value="cct" />
134-->[com.google.android.datatransport:transport-backend-cct:2.3.3] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\62f67fda4d973d784ed6d16c274509a5\transformed\transport-backend-cct-2.3.3\AndroidManifest.xml:34:17-36
135        </service>
136        <service
136-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:26:9-30:19
137            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.JobInfoSchedulerService"
137-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:27:13-117
138            android:exported="false"
138-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:28:13-37
139            android:permission="android.permission.BIND_JOB_SERVICE" >
139-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:29:13-69
140        </service>
141
142        <receiver
142-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:32:9-34:40
143            android:name="com.google.android.datatransport.runtime.scheduling.jobscheduling.AlarmManagerSchedulerBroadcastReceiver"
143-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:33:13-132
144            android:exported="false" />
144-->[com.google.android.datatransport:transport-runtime:2.2.6] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\53dbc1e4706c8938f397d542498cd2dc\transformed\transport-runtime-2.2.6\AndroidManifest.xml:34:13-37
145        <receiver
145-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:34:9-52:20
146            android:name="androidx.profileinstaller.ProfileInstallReceiver"
146-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:35:13-76
147            android:directBootAware="false"
147-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:36:13-44
148            android:enabled="true"
148-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:37:13-35
149            android:exported="true"
149-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:38:13-36
150            android:permission="android.permission.DUMP" >
150-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:39:13-57
151            <intent-filter>
151-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:40:13-42:29
152                <action android:name="androidx.profileinstaller.action.INSTALL_PROFILE" />
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:17-91
152-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:41:25-88
153            </intent-filter>
154            <intent-filter>
154-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:43:13-45:29
155                <action android:name="androidx.profileinstaller.action.SKIP_FILE" />
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:17-85
155-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:44:25-82
156            </intent-filter>
157            <intent-filter>
157-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:46:13-48:29
158                <action android:name="androidx.profileinstaller.action.SAVE_PROFILE" />
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:17-88
158-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:47:25-85
159            </intent-filter>
160            <intent-filter>
160-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:49:13-51:29
161                <action android:name="androidx.profileinstaller.action.BENCHMARK_OPERATION" />
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:17-95
161-->[androidx.profileinstaller:profileinstaller:1.3.1] C:\Users\<USER>\.gradle\caches\8.11.1\transforms\ad40a95904d22e4f36c088f0516ab14e\transformed\profileinstaller-1.3.1\AndroidManifest.xml:50:25-92
162            </intent-filter>
163        </receiver>
164    </application>
165
166</manifest>
