  Manifest android  CAMERA android.Manifest.permission  Activity android.app  ActivityResultContracts android.app.Activity  Boolean android.app.Activity  Box android.app.Activity  
CameraView android.app.Activity  
ContextCompat android.app.Activity  Face android.app.Activity  FaceOverlay android.app.Activity  	FaceTheme android.app.Activity  List android.app.Activity  Manifest android.app.Activity  Modifier android.app.Activity  PackageManager android.app.Activity  	emptyList android.app.Activity  fillMaxSize android.app.Activity  getValue android.app.Activity  mutableStateOf android.app.Activity  onCreate android.app.Activity  provideDelegate android.app.Activity  remember android.app.Activity  
setContent android.app.Activity  setValue android.app.Activity  Context android.content  ActivityResultContracts android.content.Context  Boolean android.content.Context  Box android.content.Context  
CameraView android.content.Context  
ContextCompat android.content.Context  Face android.content.Context  FaceOverlay android.content.Context  	FaceTheme android.content.Context  List android.content.Context  Manifest android.content.Context  Modifier android.content.Context  PackageManager android.content.Context  	emptyList android.content.Context  fillMaxSize android.content.Context  getValue android.content.Context  mutableStateOf android.content.Context  provideDelegate android.content.Context  remember android.content.Context  
setContent android.content.Context  setValue android.content.Context  ActivityResultContracts android.content.ContextWrapper  Boolean android.content.ContextWrapper  Box android.content.ContextWrapper  
CameraView android.content.ContextWrapper  
ContextCompat android.content.ContextWrapper  Face android.content.ContextWrapper  FaceOverlay android.content.ContextWrapper  	FaceTheme android.content.ContextWrapper  List android.content.ContextWrapper  Manifest android.content.ContextWrapper  Modifier android.content.ContextWrapper  PackageManager android.content.ContextWrapper  	emptyList android.content.ContextWrapper  fillMaxSize android.content.ContextWrapper  getValue android.content.ContextWrapper  mutableStateOf android.content.ContextWrapper  provideDelegate android.content.ContextWrapper  remember android.content.ContextWrapper  
setContent android.content.ContextWrapper  setValue android.content.ContextWrapper  PackageManager android.content.pm  PERMISSION_GRANTED !android.content.pm.PackageManager  PointF android.graphics  Rect android.graphics  x android.graphics.PointF  y android.graphics.PointF  bottom android.graphics.Rect  left android.graphics.Rect  right android.graphics.Rect  top android.graphics.Rect  Image 
android.media  Build 
android.os  Bundle 
android.os  SDK_INT android.os.Build.VERSION  S android.os.Build.VERSION_CODES  Log android.util  e android.util.Log  ActivityResultContracts  android.view.ContextThemeWrapper  Boolean  android.view.ContextThemeWrapper  Box  android.view.ContextThemeWrapper  
CameraView  android.view.ContextThemeWrapper  
ContextCompat  android.view.ContextThemeWrapper  Face  android.view.ContextThemeWrapper  FaceOverlay  android.view.ContextThemeWrapper  	FaceTheme  android.view.ContextThemeWrapper  List  android.view.ContextThemeWrapper  Manifest  android.view.ContextThemeWrapper  Modifier  android.view.ContextThemeWrapper  PackageManager  android.view.ContextThemeWrapper  	emptyList  android.view.ContextThemeWrapper  fillMaxSize  android.view.ContextThemeWrapper  getValue  android.view.ContextThemeWrapper  mutableStateOf  android.view.ContextThemeWrapper  provideDelegate  android.view.ContextThemeWrapper  remember  android.view.ContextThemeWrapper  
setContent  android.view.ContextThemeWrapper  setValue  android.view.ContextThemeWrapper  ComponentActivity androidx.activity  ActivityResultContracts #androidx.activity.ComponentActivity  Boolean #androidx.activity.ComponentActivity  Box #androidx.activity.ComponentActivity  
CameraView #androidx.activity.ComponentActivity  
ContextCompat #androidx.activity.ComponentActivity  Face #androidx.activity.ComponentActivity  FaceOverlay #androidx.activity.ComponentActivity  	FaceTheme #androidx.activity.ComponentActivity  List #androidx.activity.ComponentActivity  Manifest #androidx.activity.ComponentActivity  Modifier #androidx.activity.ComponentActivity  PackageManager #androidx.activity.ComponentActivity  	emptyList #androidx.activity.ComponentActivity  fillMaxSize #androidx.activity.ComponentActivity  getValue #androidx.activity.ComponentActivity  mutableStateOf #androidx.activity.ComponentActivity  onCreate #androidx.activity.ComponentActivity  provideDelegate #androidx.activity.ComponentActivity  registerForActivityResult #androidx.activity.ComponentActivity  remember #androidx.activity.ComponentActivity  
setContent #androidx.activity.ComponentActivity  setValue #androidx.activity.ComponentActivity  
setContent androidx.activity.compose  ActivityResultCallback androidx.activity.result  ActivityResultLauncher androidx.activity.result  <SAM-CONSTRUCTOR> /androidx.activity.result.ActivityResultCallback  launch /androidx.activity.result.ActivityResultLauncher  ActivityResultContracts !androidx.activity.result.contract  RequestPermission 9androidx.activity.result.contract.ActivityResultContracts  Camera androidx.camera.core  CameraSelector androidx.camera.core  ConcurrentCamera androidx.camera.core  
ImageAnalysis androidx.camera.core  	ImageInfo androidx.camera.core  
ImageProxy androidx.camera.core  Preview androidx.camera.core  DEFAULT_FRONT_CAMERA #androidx.camera.core.CameraSelector  Analyzer "androidx.camera.core.ImageAnalysis  Builder "androidx.camera.core.ImageAnalysis  STRATEGY_KEEP_ONLY_LATEST "androidx.camera.core.ImageAnalysis  also "androidx.camera.core.ImageAnalysis  setAnalyzer "androidx.camera.core.ImageAnalysis  <SAM-CONSTRUCTOR> +androidx.camera.core.ImageAnalysis.Analyzer  build *androidx.camera.core.ImageAnalysis.Builder  setBackpressureStrategy *androidx.camera.core.ImageAnalysis.Builder  rotationDegrees androidx.camera.core.ImageInfo  close androidx.camera.core.ImageProxy  image androidx.camera.core.ImageProxy  	imageInfo androidx.camera.core.ImageProxy  Builder androidx.camera.core.Preview  SurfaceProvider androidx.camera.core.Preview  also androidx.camera.core.Preview  setSurfaceProvider androidx.camera.core.Preview  build $androidx.camera.core.Preview.Builder  ProcessCameraProvider androidx.camera.lifecycle  bindToLifecycle /androidx.camera.lifecycle.ProcessCameraProvider  getInstance /androidx.camera.lifecycle.ProcessCameraProvider  	unbindAll /androidx.camera.lifecycle.ProcessCameraProvider  PreviewView androidx.camera.view  surfaceProvider  androidx.camera.view.PreviewView  Canvas androidx.compose.foundation  isSystemInDarkTheme androidx.compose.foundation  Box "androidx.compose.foundation.layout  BoxScope "androidx.compose.foundation.layout  fillMaxSize "androidx.compose.foundation.layout  
CameraView +androidx.compose.foundation.layout.BoxScope  FaceOverlay +androidx.compose.foundation.layout.BoxScope  ColorScheme androidx.compose.material3  
MaterialTheme androidx.compose.material3  
Typography androidx.compose.material3  darkColorScheme androidx.compose.material3  dynamicDarkColorScheme androidx.compose.material3  dynamicLightColorScheme androidx.compose.material3  lightColorScheme androidx.compose.material3  ActivityResultContracts androidx.compose.runtime  Boolean androidx.compose.runtime  Box androidx.compose.runtime  Bundle androidx.compose.runtime  CameraSelector androidx.compose.runtime  
CameraView androidx.compose.runtime  Color androidx.compose.runtime  ComponentActivity androidx.compose.runtime  
Composable androidx.compose.runtime  
ContextCompat androidx.compose.runtime  	Exception androidx.compose.runtime  	Executors androidx.compose.runtime  Face androidx.compose.runtime  
FaceDetection androidx.compose.runtime  FaceDetectorOptions androidx.compose.runtime  FaceOverlay androidx.compose.runtime  	FaceTheme androidx.compose.runtime  
ImageAnalysis androidx.compose.runtime  
InputImage androidx.compose.runtime  Int androidx.compose.runtime  List androidx.compose.runtime  Log androidx.compose.runtime  Manifest androidx.compose.runtime  Modifier androidx.compose.runtime  MutableState androidx.compose.runtime  Offset androidx.compose.runtime  PackageManager androidx.compose.runtime  Preview androidx.compose.runtime  ProcessCameraProvider androidx.compose.runtime  ProvidableCompositionLocal androidx.compose.runtime  Stroke androidx.compose.runtime  Unit androidx.compose.runtime  also androidx.compose.runtime  androidx androidx.compose.runtime  	emptyList androidx.compose.runtime  fillMaxSize androidx.compose.runtime  forEach androidx.compose.runtime  getValue androidx.compose.runtime  mutableStateOf androidx.compose.runtime  provideDelegate androidx.compose.runtime  remember androidx.compose.runtime  setValue androidx.compose.runtime  
getCurrent )androidx.compose.runtime.CompositionLocal  setValue %androidx.compose.runtime.MutableState  current 3androidx.compose.runtime.ProvidableCompositionLocal  ComposableFunction0 !androidx.compose.runtime.internal  ComposableFunction1 !androidx.compose.runtime.internal  Modifier androidx.compose.ui  	Companion androidx.compose.ui.Modifier  fillMaxSize androidx.compose.ui.Modifier  fillMaxSize &androidx.compose.ui.Modifier.Companion  Offset androidx.compose.ui.geometry  Size androidx.compose.ui.geometry  height !androidx.compose.ui.geometry.Size  width !androidx.compose.ui.geometry.Size  Color androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Color  Red "androidx.compose.ui.graphics.Color  Yellow "androidx.compose.ui.graphics.Color  Red ,androidx.compose.ui.graphics.Color.Companion  Yellow ,androidx.compose.ui.graphics.Color.Companion  	DrawScope &androidx.compose.ui.graphics.drawscope  Stroke &androidx.compose.ui.graphics.drawscope  Color 0androidx.compose.ui.graphics.drawscope.DrawScope  Offset 0androidx.compose.ui.graphics.drawscope.DrawScope  Stroke 0androidx.compose.ui.graphics.drawscope.DrawScope  androidx 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawCircle 0androidx.compose.ui.graphics.drawscope.DrawScope  drawRect 0androidx.compose.ui.graphics.drawscope.DrawScope  size 0androidx.compose.ui.graphics.drawscope.DrawScope  LocalContext androidx.compose.ui.platform  LocalLifecycleOwner androidx.compose.ui.platform  	TextStyle androidx.compose.ui.text  
FontFamily androidx.compose.ui.text.font  
FontWeight androidx.compose.ui.text.font  SystemFontFamily androidx.compose.ui.text.font  	Companion (androidx.compose.ui.text.font.FontFamily  Default (androidx.compose.ui.text.font.FontFamily  Default 2androidx.compose.ui.text.font.FontFamily.Companion  	Companion (androidx.compose.ui.text.font.FontWeight  Normal (androidx.compose.ui.text.font.FontWeight  Normal 2androidx.compose.ui.text.font.FontWeight.Companion  TextUnit androidx.compose.ui.unit  sp androidx.compose.ui.unit  AndroidView androidx.compose.ui.viewinterop  ActivityResultContracts #androidx.core.app.ComponentActivity  Boolean #androidx.core.app.ComponentActivity  Box #androidx.core.app.ComponentActivity  Bundle #androidx.core.app.ComponentActivity  
CameraView #androidx.core.app.ComponentActivity  
ContextCompat #androidx.core.app.ComponentActivity  Face #androidx.core.app.ComponentActivity  FaceOverlay #androidx.core.app.ComponentActivity  	FaceTheme #androidx.core.app.ComponentActivity  List #androidx.core.app.ComponentActivity  Manifest #androidx.core.app.ComponentActivity  Modifier #androidx.core.app.ComponentActivity  PackageManager #androidx.core.app.ComponentActivity  	emptyList #androidx.core.app.ComponentActivity  fillMaxSize #androidx.core.app.ComponentActivity  getValue #androidx.core.app.ComponentActivity  mutableStateOf #androidx.core.app.ComponentActivity  provideDelegate #androidx.core.app.ComponentActivity  remember #androidx.core.app.ComponentActivity  
setContent #androidx.core.app.ComponentActivity  setValue #androidx.core.app.ComponentActivity  
ContextCompat androidx.core.content  checkSelfPermission #androidx.core.content.ContextCompat  LifecycleOwner androidx.lifecycle  OnCompleteListener com.google.android.gms.tasks  OnFailureListener com.google.android.gms.tasks  OnSuccessListener com.google.android.gms.tasks  Task com.google.android.gms.tasks  <SAM-CONSTRUCTOR> /com.google.android.gms.tasks.OnCompleteListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnFailureListener  <SAM-CONSTRUCTOR> .com.google.android.gms.tasks.OnSuccessListener  addOnCompleteListener !com.google.android.gms.tasks.Task  addOnFailureListener !com.google.android.gms.tasks.Task  addOnSuccessListener !com.google.android.gms.tasks.Task  ListenableFuture !com.google.common.util.concurrent  get 2com.google.common.util.concurrent.ListenableFuture  
InputImage com.google.mlkit.vision.common  fromMediaImage )com.google.mlkit.vision.common.InputImage  height )com.google.mlkit.vision.common.InputImage  width )com.google.mlkit.vision.common.InputImage  Face com.google.mlkit.vision.face  
FaceDetection com.google.mlkit.vision.face  FaceDetector com.google.mlkit.vision.face  FaceDetectorOptions com.google.mlkit.vision.face  allLandmarks !com.google.mlkit.vision.face.Face  boundingBox !com.google.mlkit.vision.face.Face  	getClient *com.google.mlkit.vision.face.FaceDetection  process )com.google.mlkit.vision.face.FaceDetector  Builder 0com.google.mlkit.vision.face.FaceDetectorOptions  CLASSIFICATION_MODE_NONE 0com.google.mlkit.vision.face.FaceDetectorOptions  LANDMARK_MODE_ALL 0com.google.mlkit.vision.face.FaceDetectorOptions  PERFORMANCE_MODE_FAST 0com.google.mlkit.vision.face.FaceDetectorOptions  build 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setClassificationMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setLandmarkMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setPerformanceMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  position )com.google.mlkit.vision.face.FaceLandmark  ActivityResultContracts com.wendy.face  Boolean com.wendy.face  Box com.wendy.face  Bundle com.wendy.face  CameraSelector com.wendy.face  
CameraView com.wendy.face  Color com.wendy.face  ComponentActivity com.wendy.face  
Composable com.wendy.face  
ContextCompat com.wendy.face  	Exception com.wendy.face  	Executors com.wendy.face  Face com.wendy.face  
FaceDetection com.wendy.face  FaceDetectorOptions com.wendy.face  FaceOverlay com.wendy.face  	FaceTheme com.wendy.face  
ImageAnalysis com.wendy.face  
InputImage com.wendy.face  Int com.wendy.face  List com.wendy.face  Log com.wendy.face  MainActivity com.wendy.face  Manifest com.wendy.face  Modifier com.wendy.face  Offset com.wendy.face  PackageManager com.wendy.face  Preview com.wendy.face  ProcessCameraProvider com.wendy.face  Stroke com.wendy.face  Unit com.wendy.face  also com.wendy.face  androidx com.wendy.face  	emptyList com.wendy.face  fillMaxSize com.wendy.face  forEach com.wendy.face  getValue com.wendy.face  mutableStateOf com.wendy.face  provideDelegate com.wendy.face  remember com.wendy.face  setValue com.wendy.face  ActivityResultContracts com.wendy.face.MainActivity  Box com.wendy.face.MainActivity  
CameraView com.wendy.face.MainActivity  
ContextCompat com.wendy.face.MainActivity  FaceOverlay com.wendy.face.MainActivity  	FaceTheme com.wendy.face.MainActivity  Manifest com.wendy.face.MainActivity  Modifier com.wendy.face.MainActivity  PackageManager com.wendy.face.MainActivity  	emptyList com.wendy.face.MainActivity  fillMaxSize com.wendy.face.MainActivity  getValue com.wendy.face.MainActivity  mutableStateOf com.wendy.face.MainActivity  provideDelegate com.wendy.face.MainActivity  registerForActivityResult com.wendy.face.MainActivity  remember com.wendy.face.MainActivity  requestPermissionLauncher com.wendy.face.MainActivity  
setContent com.wendy.face.MainActivity  setValue com.wendy.face.MainActivity  Boolean com.wendy.face.ui.theme  Build com.wendy.face.ui.theme  
Composable com.wendy.face.ui.theme  DarkColorScheme com.wendy.face.ui.theme  	FaceTheme com.wendy.face.ui.theme  
FontFamily com.wendy.face.ui.theme  
FontWeight com.wendy.face.ui.theme  LightColorScheme com.wendy.face.ui.theme  Pink40 com.wendy.face.ui.theme  Pink80 com.wendy.face.ui.theme  Purple40 com.wendy.face.ui.theme  Purple80 com.wendy.face.ui.theme  PurpleGrey40 com.wendy.face.ui.theme  PurpleGrey80 com.wendy.face.ui.theme  
Typography com.wendy.face.ui.theme  Unit com.wendy.face.ui.theme  	Exception 	java.lang  	Executors java.util.concurrent  newSingleThreadExecutor java.util.concurrent.Executors  	Function0 kotlin  	Function1 kotlin  	Function3 kotlin  Nothing kotlin  also kotlin  sp 
kotlin.Double  div kotlin.Float  minus kotlin.Float  times kotlin.Float  invoke kotlin.Function3  	compareTo 
kotlin.Int  times 
kotlin.Int  toFloat 
kotlin.Int  Iterator kotlin.collections  List kotlin.collections  	emptyList kotlin.collections  forEach kotlin.collections  hasNext kotlin.collections.Iterator  next kotlin.collections.Iterator  iterator kotlin.collections.List  KMutableProperty0 kotlin.reflect  forEach kotlin.sequences  forEach kotlin.text  WRITE_EXTERNAL_STORAGE android.Manifest.permission  android android.app.Activity  
ContentValues android.content  android android.content.ContentValues  apply android.content.ContentValues  put android.content.ContentValues  android android.content.Context  contentResolver android.content.Context  android android.content.ContextWrapper  Uri android.net  let android.net.Uri  P android.os.Build.VERSION_CODES  EXTERNAL_CONTENT_URI (android.provider.MediaStore.Images.Media  
RELATIVE_PATH (android.provider.MediaStore.Images.Media  DISPLAY_NAME (android.provider.MediaStore.MediaColumns  	MIME_TYPE (android.provider.MediaStore.MediaColumns  
RELATIVE_PATH (android.provider.MediaStore.MediaColumns  d android.util.Log  android  android.view.ContextThemeWrapper  android #androidx.activity.ComponentActivity  ImageCapture androidx.camera.core  ImageCaptureException androidx.camera.core  DEFAULT_BACK_CAMERA #androidx.camera.core.CameraSelector  Builder !androidx.camera.core.ImageCapture  CAPTURE_MODE_MAXIMIZE_QUALITY !androidx.camera.core.ImageCapture  OnImageSavedCallback !androidx.camera.core.ImageCapture  OutputFileOptions !androidx.camera.core.ImageCapture  OutputFileResults !androidx.camera.core.ImageCapture  let !androidx.camera.core.ImageCapture  takePicture !androidx.camera.core.ImageCapture  build )androidx.camera.core.ImageCapture.Builder  setCaptureMode )androidx.camera.core.ImageCapture.Builder  Builder 3androidx.camera.core.ImageCapture.OutputFileOptions  build ;androidx.camera.core.ImageCapture.OutputFileOptions.Builder  savedUri 3androidx.camera.core.ImageCapture.OutputFileResults  message *androidx.camera.core.ImageCaptureException  Arrangement "androidx.compose.foundation.layout  Column "androidx.compose.foundation.layout  Row "androidx.compose.foundation.layout  RowScope "androidx.compose.foundation.layout  fillMaxWidth "androidx.compose.foundation.layout  padding "androidx.compose.foundation.layout  size "androidx.compose.foundation.layout  HorizontalOrVertical .androidx.compose.foundation.layout.Arrangement  SpaceEvenly .androidx.compose.foundation.layout.Arrangement  AndroidView +androidx.compose.foundation.layout.BoxScope  Arrangement +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.BoxScope  CameraSelector +androidx.compose.foundation.layout.BoxScope  ImageCapture +androidx.compose.foundation.layout.BoxScope  Log +androidx.compose.foundation.layout.BoxScope  Modifier +androidx.compose.foundation.layout.BoxScope  Preview +androidx.compose.foundation.layout.BoxScope  PreviewView +androidx.compose.foundation.layout.BoxScope  Row +androidx.compose.foundation.layout.BoxScope  Text +androidx.compose.foundation.layout.BoxScope  align +androidx.compose.foundation.layout.BoxScope  also +androidx.compose.foundation.layout.BoxScope  androidx +androidx.compose.foundation.layout.BoxScope  dp +androidx.compose.foundation.layout.BoxScope  fillMaxSize +androidx.compose.foundation.layout.BoxScope  fillMaxWidth +androidx.compose.foundation.layout.BoxScope  let +androidx.compose.foundation.layout.BoxScope  padding +androidx.compose.foundation.layout.BoxScope  takePicture +androidx.compose.foundation.layout.BoxScope  Button +androidx.compose.foundation.layout.RowScope  Text +androidx.compose.foundation.layout.RowScope  let +androidx.compose.foundation.layout.RowScope  takePicture +androidx.compose.foundation.layout.RowScope  Button androidx.compose.material3  FloatingActionButton androidx.compose.material3  Text androidx.compose.material3  AndroidView androidx.compose.runtime  Arrangement androidx.compose.runtime  Button androidx.compose.runtime  	DrawScope androidx.compose.runtime  FaceContour androidx.compose.runtime  Float androidx.compose.runtime  ImageCapture androidx.compose.runtime  ImageCaptureException androidx.compose.runtime  Locale androidx.compose.runtime  Path androidx.compose.runtime  PreviewView androidx.compose.runtime  Row androidx.compose.runtime  System androidx.compose.runtime  Text androidx.compose.runtime  android androidx.compose.runtime  apply androidx.compose.runtime  com androidx.compose.runtime  fillMaxWidth androidx.compose.runtime  let androidx.compose.runtime  listOf androidx.compose.runtime  padding androidx.compose.runtime  takePicture androidx.compose.runtime  until androidx.compose.runtime  OnImageSavedCallback %androidx.compose.runtime.ImageCapture  OutputFileResults %androidx.compose.runtime.ImageCapture  content  androidx.compose.runtime.android  Context (androidx.compose.runtime.android.content  google androidx.compose.runtime.com  mlkit #androidx.compose.runtime.com.google  vision )androidx.compose.runtime.com.google.mlkit  face 0androidx.compose.runtime.com.google.mlkit.vision  FaceDetector 5androidx.compose.runtime.com.google.mlkit.vision.face  	Alignment androidx.compose.ui  BottomCenter androidx.compose.ui.Alignment  	Companion androidx.compose.ui.Alignment  BottomCenter 'androidx.compose.ui.Alignment.Companion  align androidx.compose.ui.Modifier  fillMaxWidth androidx.compose.ui.Modifier  padding androidx.compose.ui.Modifier  fillMaxWidth &androidx.compose.ui.Modifier.Companion  Path androidx.compose.ui.graphics  Blue "androidx.compose.ui.graphics.Color  Cyan "androidx.compose.ui.graphics.Color  Green "androidx.compose.ui.graphics.Color  Magenta "androidx.compose.ui.graphics.Color  White "androidx.compose.ui.graphics.Color  Blue ,androidx.compose.ui.graphics.Color.Companion  Cyan ,androidx.compose.ui.graphics.Color.Companion  Green ,androidx.compose.ui.graphics.Color.Companion  Magenta ,androidx.compose.ui.graphics.Color.Companion  White ,androidx.compose.ui.graphics.Color.Companion  close !androidx.compose.ui.graphics.Path  lineTo !androidx.compose.ui.graphics.Path  moveTo !androidx.compose.ui.graphics.Path  FaceContour 0androidx.compose.ui.graphics.drawscope.DrawScope  Path 0androidx.compose.ui.graphics.drawscope.DrawScope  com 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFaceContours 0androidx.compose.ui.graphics.drawscope.DrawScope  drawPath 0androidx.compose.ui.graphics.drawscope.DrawScope  let 0androidx.compose.ui.graphics.drawscope.DrawScope  listOf 0androidx.compose.ui.graphics.drawscope.DrawScope  until 0androidx.compose.ui.graphics.drawscope.DrawScope  Dp androidx.compose.ui.unit  dp androidx.compose.ui.unit  getDp androidx.compose.ui.unit  android #androidx.core.app.ComponentActivity  getMainExecutor #androidx.core.content.ContextCompat  fromFilePath )com.google.mlkit.vision.common.InputImage  FaceContour com.google.mlkit.vision.face  
getContour !com.google.mlkit.vision.face.Face  FACE (com.google.mlkit.vision.face.FaceContour  LEFT_EYE (com.google.mlkit.vision.face.FaceContour  LEFT_EYEBROW_BOTTOM (com.google.mlkit.vision.face.FaceContour  LEFT_EYEBROW_TOP (com.google.mlkit.vision.face.FaceContour  LOWER_LIP_BOTTOM (com.google.mlkit.vision.face.FaceContour  
LOWER_LIP_TOP (com.google.mlkit.vision.face.FaceContour  NOSE_BOTTOM (com.google.mlkit.vision.face.FaceContour  NOSE_BRIDGE (com.google.mlkit.vision.face.FaceContour  	RIGHT_EYE (com.google.mlkit.vision.face.FaceContour  RIGHT_EYEBROW_BOTTOM (com.google.mlkit.vision.face.FaceContour  RIGHT_EYEBROW_TOP (com.google.mlkit.vision.face.FaceContour  UPPER_LIP_BOTTOM (com.google.mlkit.vision.face.FaceContour  
UPPER_LIP_TOP (com.google.mlkit.vision.face.FaceContour  let (com.google.mlkit.vision.face.FaceContour  points (com.google.mlkit.vision.face.FaceContour  CLASSIFICATION_MODE_ALL 0com.google.mlkit.vision.face.FaceDetectorOptions  CONTOUR_MODE_ALL 0com.google.mlkit.vision.face.FaceDetectorOptions  PERFORMANCE_MODE_ACCURATE 0com.google.mlkit.vision.face.FaceDetectorOptions  enableTracking 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setContourMode 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  setMinFaceSize 8com.google.mlkit.vision.face.FaceDetectorOptions.Builder  LEFT_EAR )com.google.mlkit.vision.face.FaceLandmark  LEFT_EYE )com.google.mlkit.vision.face.FaceLandmark  MOUTH_BOTTOM )com.google.mlkit.vision.face.FaceLandmark  
MOUTH_LEFT )com.google.mlkit.vision.face.FaceLandmark  MOUTH_RIGHT )com.google.mlkit.vision.face.FaceLandmark  	NOSE_BASE )com.google.mlkit.vision.face.FaceLandmark  	RIGHT_EAR )com.google.mlkit.vision.face.FaceLandmark  	RIGHT_EYE )com.google.mlkit.vision.face.FaceLandmark  landmarkType )com.google.mlkit.vision.face.FaceLandmark  AndroidView com.wendy.face  Arrangement com.wendy.face  Button com.wendy.face  	DrawScope com.wendy.face  FaceContour com.wendy.face  Float com.wendy.face  ImageCapture com.wendy.face  ImageCaptureException com.wendy.face  Locale com.wendy.face  Path com.wendy.face  PreviewView com.wendy.face  Row com.wendy.face  System com.wendy.face  Text com.wendy.face  android com.wendy.face  apply com.wendy.face  com com.wendy.face  drawFaceContours com.wendy.face  fillMaxWidth com.wendy.face  let com.wendy.face  listOf com.wendy.face  padding com.wendy.face  takePicture com.wendy.face  until com.wendy.face  OnImageSavedCallback com.wendy.face.ImageCapture  OutputFileResults com.wendy.face.ImageCapture  android com.wendy.face.MainActivity  content com.wendy.face.android  Context com.wendy.face.android.content  google com.wendy.face.com  mlkit com.wendy.face.com.google  vision com.wendy.face.com.google.mlkit  face &com.wendy.face.com.google.mlkit.vision  FaceDetector +com.wendy.face.com.google.mlkit.vision.face  File java.io  currentTimeMillis java.lang.System  SimpleDateFormat 	java.text  format java.text.DateFormat  format java.text.Format  format java.text.SimpleDateFormat  Locale 	java.util  US java.util.Locale  Executor java.util.concurrent  apply kotlin  let kotlin  not kotlin.Boolean  message kotlin.Throwable  IntIterator kotlin.collections  listOf kotlin.collections  hasNext kotlin.collections.IntIterator  next kotlin.collections.IntIterator  get kotlin.collections.MutableList  size kotlin.collections.MutableList  	CharRange 
kotlin.ranges  IntRange 
kotlin.ranges  	LongRange 
kotlin.ranges  	UIntRange 
kotlin.ranges  
ULongRange 
kotlin.ranges  until 
kotlin.ranges  iterator kotlin.ranges.IntProgression  iterator kotlin.ranges.IntRange  addListener 2com.google.common.util.concurrent.ListenableFuture  Runnable 	java.lang  <SAM-CONSTRUCTOR> java.lang.Runnable  Log android.app.Activity  String android.app.Activity  
isNotEmpty android.app.Activity  
mutableListOf android.app.Activity  toTypedArray android.app.Activity  Log android.content.Context  String android.content.Context  
isNotEmpty android.content.Context  
mutableListOf android.content.Context  toTypedArray android.content.Context  Log android.content.ContextWrapper  String android.content.ContextWrapper  
isNotEmpty android.content.ContextWrapper  
mutableListOf android.content.ContextWrapper  toTypedArray android.content.ContextWrapper  w android.util.Log  Log  android.view.ContextThemeWrapper  String  android.view.ContextThemeWrapper  
isNotEmpty  android.view.ContextThemeWrapper  
mutableListOf  android.view.ContextThemeWrapper  toTypedArray  android.view.ContextThemeWrapper  Log #androidx.activity.ComponentActivity  String #androidx.activity.ComponentActivity  
isNotEmpty #androidx.activity.ComponentActivity  
mutableListOf #androidx.activity.ComponentActivity  toTypedArray #androidx.activity.ComponentActivity  RequestMultiplePermissions 9androidx.activity.result.contract.ActivityResultContracts  Log +androidx.compose.foundation.layout.RowScope  String androidx.compose.runtime  
isNotEmpty androidx.compose.runtime  
mutableListOf androidx.compose.runtime  toTypedArray androidx.compose.runtime  Log #androidx.core.app.ComponentActivity  String #androidx.core.app.ComponentActivity  
isNotEmpty #androidx.core.app.ComponentActivity  
mutableListOf #androidx.core.app.ComponentActivity  toTypedArray #androidx.core.app.ComponentActivity  String com.wendy.face  
isNotEmpty com.wendy.face  
mutableListOf com.wendy.face  toTypedArray com.wendy.face  Log com.wendy.face.MainActivity  
isNotEmpty com.wendy.face.MainActivity  
mutableListOf com.wendy.face.MainActivity  "requestMultiplePermissionsLauncher com.wendy.face.MainActivity  toTypedArray com.wendy.face.MainActivity  Array kotlin  invoke kotlin.Function0  MutableList kotlin.collections  Set kotlin.collections  
isNotEmpty kotlin.collections  
mutableListOf kotlin.collections  toTypedArray kotlin.collections  Entry kotlin.collections.Map  entries kotlin.collections.Map  key kotlin.collections.Map.Entry  value kotlin.collections.Map.Entry  add kotlin.collections.MutableList  
isNotEmpty kotlin.collections.MutableList  toTypedArray kotlin.collections.MutableList  
isNotEmpty kotlin.text  let  androidx.camera.view.PreviewView  LaunchedEffect androidx.compose.runtime  LaunchedEffect com.wendy.face  SuspendFunction1 kotlin.coroutines  CoroutineScope kotlinx.coroutines  CameraSelector !kotlinx.coroutines.CoroutineScope  ImageCapture !kotlinx.coroutines.CoroutineScope  Log !kotlinx.coroutines.CoroutineScope  Preview !kotlinx.coroutines.CoroutineScope  also !kotlinx.coroutines.CoroutineScope  androidx !kotlinx.coroutines.CoroutineScope  let !kotlinx.coroutines.CoroutineScope  SimpleCameraPreview android.app.Activity  SimpleCameraPreview android.content.Context  SimpleCameraPreview android.content.ContextWrapper  SimpleCameraPreview  android.view.ContextThemeWrapper  SimpleCameraPreview #androidx.activity.ComponentActivity  
ContextCompat +androidx.compose.foundation.layout.BoxScope  SimpleCameraPreview androidx.compose.runtime  SimpleCameraPreview #androidx.core.app.ComponentActivity  SimpleCameraPreview com.wendy.face  TestCameraActivity com.wendy.face  ActivityResultContracts !com.wendy.face.TestCameraActivity  
ContextCompat !com.wendy.face.TestCameraActivity  	FaceTheme !com.wendy.face.TestCameraActivity  Log !com.wendy.face.TestCameraActivity  Manifest !com.wendy.face.TestCameraActivity  PackageManager !com.wendy.face.TestCameraActivity  SimpleCameraPreview !com.wendy.face.TestCameraActivity  registerForActivityResult !com.wendy.face.TestCameraActivity  requestPermissionLauncher !com.wendy.face.TestCameraActivity  
setContent !com.wendy.face.TestCameraActivity  Bitmap android.app.Activity  Button android.app.Activity  ContentScale android.app.Activity  Image android.app.Activity  Text android.app.Activity  Uri android.app.Activity  align android.app.Activity  androidx android.app.Activity  
asImageBitmap android.app.Activity  dp android.app.Activity  let android.app.Activity  padding android.app.Activity  openInputStream android.content.ContentResolver  Bitmap android.content.Context  Button android.content.Context  ContentScale android.content.Context  Image android.content.Context  Text android.content.Context  Uri android.content.Context  align android.content.Context  androidx android.content.Context  
asImageBitmap android.content.Context  dp android.content.Context  let android.content.Context  padding android.content.Context  Bitmap android.content.ContextWrapper  Button android.content.ContextWrapper  ContentScale android.content.ContextWrapper  Image android.content.ContextWrapper  Text android.content.ContextWrapper  Uri android.content.ContextWrapper  align android.content.ContextWrapper  androidx android.content.ContextWrapper  
asImageBitmap android.content.ContextWrapper  dp android.content.ContextWrapper  let android.content.ContextWrapper  padding android.content.ContextWrapper  Bitmap android.graphics  
BitmapFactory android.graphics  
asImageBitmap android.graphics.Bitmap  height android.graphics.Bitmap  let android.graphics.Bitmap  width android.graphics.Bitmap  decodeStream android.graphics.BitmapFactory  Bitmap  android.view.ContextThemeWrapper  Button  android.view.ContextThemeWrapper  ContentScale  android.view.ContextThemeWrapper  Image  android.view.ContextThemeWrapper  Text  android.view.ContextThemeWrapper  Uri  android.view.ContextThemeWrapper  align  android.view.ContextThemeWrapper  androidx  android.view.ContextThemeWrapper  
asImageBitmap  android.view.ContextThemeWrapper  dp  android.view.ContextThemeWrapper  let  android.view.ContextThemeWrapper  padding  android.view.ContextThemeWrapper  Bitmap #androidx.activity.ComponentActivity  Button #androidx.activity.ComponentActivity  ContentScale #androidx.activity.ComponentActivity  Image #androidx.activity.ComponentActivity  Text #androidx.activity.ComponentActivity  Uri #androidx.activity.ComponentActivity  align #androidx.activity.ComponentActivity  androidx #androidx.activity.ComponentActivity  
asImageBitmap #androidx.activity.ComponentActivity  dp #androidx.activity.ComponentActivity  let #androidx.activity.ComponentActivity  padding #androidx.activity.ComponentActivity  Image androidx.compose.foundation  ContentScale +androidx.compose.foundation.layout.BoxScope  Image +androidx.compose.foundation.layout.BoxScope  
asImageBitmap +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.BoxScope  Bitmap androidx.compose.runtime  
BitmapFactory androidx.compose.runtime  ContentScale androidx.compose.runtime  Image androidx.compose.runtime  Uri androidx.compose.runtime  align androidx.compose.runtime  arrayOf androidx.compose.runtime  
asImageBitmap androidx.compose.runtime  
component1 androidx.compose.runtime  
component2 androidx.compose.runtime  
component3 androidx.compose.runtime  
component4 androidx.compose.runtime  TopEnd androidx.compose.ui.Alignment  TopEnd 'androidx.compose.ui.Alignment.Companion  align &androidx.compose.ui.Modifier.Companion  ImageBitmap androidx.compose.ui.graphics  
asImageBitmap androidx.compose.ui.graphics  arrayOf 0androidx.compose.ui.graphics.drawscope.DrawScope  
component1 0androidx.compose.ui.graphics.drawscope.DrawScope  
component2 0androidx.compose.ui.graphics.drawscope.DrawScope  
component3 0androidx.compose.ui.graphics.drawscope.DrawScope  
component4 0androidx.compose.ui.graphics.drawscope.DrawScope  ContentScale androidx.compose.ui.layout  	Companion 'androidx.compose.ui.layout.ContentScale  Fit 'androidx.compose.ui.layout.ContentScale  Fit 1androidx.compose.ui.layout.ContentScale.Companion  Bitmap #androidx.core.app.ComponentActivity  Button #androidx.core.app.ComponentActivity  ContentScale #androidx.core.app.ComponentActivity  Image #androidx.core.app.ComponentActivity  Text #androidx.core.app.ComponentActivity  Uri #androidx.core.app.ComponentActivity  align #androidx.core.app.ComponentActivity  androidx #androidx.core.app.ComponentActivity  
asImageBitmap #androidx.core.app.ComponentActivity  dp #androidx.core.app.ComponentActivity  let #androidx.core.app.ComponentActivity  padding #androidx.core.app.ComponentActivity  
fromBitmap )com.google.mlkit.vision.common.InputImage  Bitmap com.wendy.face  
BitmapFactory com.wendy.face  ContentScale com.wendy.face  Image com.wendy.face  Uri com.wendy.face  align com.wendy.face  arrayOf com.wendy.face  
asImageBitmap com.wendy.face  
component1 com.wendy.face  
component2 com.wendy.face  
component3 com.wendy.face  
component4 com.wendy.face  Button com.wendy.face.MainActivity  ContentScale com.wendy.face.MainActivity  Image com.wendy.face.MainActivity  Text com.wendy.face.MainActivity  align com.wendy.face.MainActivity  androidx com.wendy.face.MainActivity  
asImageBitmap com.wendy.face.MainActivity  dp com.wendy.face.MainActivity  let com.wendy.face.MainActivity  padding com.wendy.face.MainActivity  InputStream java.io  close java.io.InputStream  	Function2 kotlin  arrayOf kotlin  
component1 kotlin.Array  
component2 kotlin.Array  
component3 kotlin.Array  
component4 kotlin.Array  	compareTo kotlin.Float  plus kotlin.Float  invoke kotlin.Function2  
component1 kotlin.collections  
component2 kotlin.collections  
component3 kotlin.collections  
component4 kotlin.collections  isEmpty kotlin.collections.List  forEachIndexed androidx.compose.runtime  Log 0androidx.compose.ui.graphics.drawscope.DrawScope  forEachIndexed com.wendy.face  forEachIndexed kotlin.collections  forEachIndexed kotlin.sequences  forEachIndexed kotlin.text  Arrangement android.app.Activity  Row android.app.Activity  spacedBy android.app.Activity  testFaceDetection android.app.Activity  Arrangement android.content.Context  Row android.content.Context  spacedBy android.content.Context  testFaceDetection android.content.Context  Arrangement android.content.ContextWrapper  Row android.content.ContextWrapper  spacedBy android.content.ContextWrapper  testFaceDetection android.content.ContextWrapper  Canvas android.graphics  Paint android.graphics  Config android.graphics.Bitmap  createBitmap android.graphics.Bitmap  
eraseColor android.graphics.Bitmap  	ARGB_8888 android.graphics.Bitmap.Config  
drawCircle android.graphics.Canvas  drawRect android.graphics.Canvas  BLUE android.graphics.Color  RED android.graphics.Color  YELLOW android.graphics.Color  Style android.graphics.Paint  android android.graphics.Paint  apply android.graphics.Paint  color android.graphics.Paint  strokeWidth android.graphics.Paint  style android.graphics.Paint  FILL android.graphics.Paint.Style  STROKE android.graphics.Paint.Style  Arrangement  android.view.ContextThemeWrapper  Row  android.view.ContextThemeWrapper  spacedBy  android.view.ContextThemeWrapper  testFaceDetection  android.view.ContextThemeWrapper  Arrangement #androidx.activity.ComponentActivity  Row #androidx.activity.ComponentActivity  spacedBy #androidx.activity.ComponentActivity  testFaceDetection #androidx.activity.ComponentActivity  
Horizontal .androidx.compose.foundation.layout.Arrangement  Vertical .androidx.compose.foundation.layout.Arrangement  spacedBy .androidx.compose.foundation.layout.Arrangement  spacedBy +androidx.compose.foundation.layout.BoxScope  testFaceDetection +androidx.compose.foundation.layout.BoxScope  	emptyList +androidx.compose.foundation.layout.RowScope  testFaceDetection +androidx.compose.foundation.layout.RowScope  spacedBy androidx.compose.runtime  testFaceDetection androidx.compose.runtime  Arrangement #androidx.core.app.ComponentActivity  Row #androidx.core.app.ComponentActivity  spacedBy #androidx.core.app.ComponentActivity  testFaceDetection #androidx.core.app.ComponentActivity  spacedBy com.wendy.face  testFaceDetection com.wendy.face  Arrangement com.wendy.face.MainActivity  Row com.wendy.face.MainActivity  spacedBy com.wendy.face.MainActivity  testFaceDetection com.wendy.face.MainActivity  	Function4 kotlin  invoke kotlin.Function4  FaceDetectionGuide android.app.Activity  FaceDetectionGuide android.content.Context  FaceDetectionGuide android.content.ContextWrapper  Matrix android.graphics  recycle android.graphics.Bitmap  
postRotate android.graphics.Matrix  
ExifInterface 
android.media  ORIENTATION_NORMAL android.media.ExifInterface  ORIENTATION_ROTATE_180 android.media.ExifInterface  ORIENTATION_ROTATE_270 android.media.ExifInterface  ORIENTATION_ROTATE_90 android.media.ExifInterface  TAG_ORIENTATION android.media.ExifInterface  getAttributeInt android.media.ExifInterface  FaceDetectionGuide  android.view.ContextThemeWrapper  FaceDetectionGuide #androidx.activity.ComponentActivity  ColumnScope "androidx.compose.foundation.layout  Spacer "androidx.compose.foundation.layout  height "androidx.compose.foundation.layout  Canvas +androidx.compose.foundation.layout.BoxScope  Color +androidx.compose.foundation.layout.BoxScope  Column +androidx.compose.foundation.layout.BoxScope  FaceDetectionGuide +androidx.compose.foundation.layout.BoxScope  
FontWeight +androidx.compose.foundation.layout.BoxScope  
MaterialTheme +androidx.compose.foundation.layout.BoxScope  Offset +androidx.compose.foundation.layout.BoxScope  
PathEffect +androidx.compose.foundation.layout.BoxScope  Spacer +androidx.compose.foundation.layout.BoxScope  Stroke +androidx.compose.foundation.layout.BoxScope  	StrokeCap +androidx.compose.foundation.layout.BoxScope  	TextAlign +androidx.compose.foundation.layout.BoxScope  dashPathEffect +androidx.compose.foundation.layout.BoxScope  floatArrayOf +androidx.compose.foundation.layout.BoxScope  height +androidx.compose.foundation.layout.BoxScope  Color .androidx.compose.foundation.layout.ColumnScope  
FontWeight .androidx.compose.foundation.layout.ColumnScope  
MaterialTheme .androidx.compose.foundation.layout.ColumnScope  Modifier .androidx.compose.foundation.layout.ColumnScope  Spacer .androidx.compose.foundation.layout.ColumnScope  Text .androidx.compose.foundation.layout.ColumnScope  	TextAlign .androidx.compose.foundation.layout.ColumnScope  dp .androidx.compose.foundation.layout.ColumnScope  height .androidx.compose.foundation.layout.ColumnScope  
typography (androidx.compose.material3.MaterialTheme  	bodyLarge %androidx.compose.material3.Typography  
headlineSmall %androidx.compose.material3.Typography  Canvas androidx.compose.runtime  Column androidx.compose.runtime  
ExifInterface androidx.compose.runtime  FaceDetectionGuide androidx.compose.runtime  
FontWeight androidx.compose.runtime  
MaterialTheme androidx.compose.runtime  
PathEffect androidx.compose.runtime  Spacer androidx.compose.runtime  	StrokeCap androidx.compose.runtime  	TextAlign androidx.compose.runtime  dashPathEffect androidx.compose.runtime  floatArrayOf androidx.compose.runtime  height androidx.compose.runtime  loadAndRotateBitmap androidx.compose.runtime  CenterHorizontally androidx.compose.ui.Alignment  
Horizontal androidx.compose.ui.Alignment  	TopCenter androidx.compose.ui.Alignment  CenterHorizontally 'androidx.compose.ui.Alignment.Companion  	TopCenter 'androidx.compose.ui.Alignment.Companion  height androidx.compose.ui.Modifier  height &androidx.compose.ui.Modifier.Companion  
PathEffect androidx.compose.ui.graphics  	StrokeCap androidx.compose.ui.graphics  
StrokeJoin androidx.compose.ui.graphics  Black "androidx.compose.ui.graphics.Color  copy "androidx.compose.ui.graphics.Color  Black ,androidx.compose.ui.graphics.Color.Companion  	Companion 'androidx.compose.ui.graphics.PathEffect  dashPathEffect 'androidx.compose.ui.graphics.PathEffect  dashPathEffect 1androidx.compose.ui.graphics.PathEffect.Companion  	Companion &androidx.compose.ui.graphics.StrokeCap  Round &androidx.compose.ui.graphics.StrokeCap  Round 0androidx.compose.ui.graphics.StrokeCap.Companion  
PathEffect 0androidx.compose.ui.graphics.drawscope.DrawScope  	StrokeCap 0androidx.compose.ui.graphics.drawscope.DrawScope  dashPathEffect 0androidx.compose.ui.graphics.drawscope.DrawScope  dp 0androidx.compose.ui.graphics.drawscope.DrawScope  drawLine 0androidx.compose.ui.graphics.drawscope.DrawScope  drawOval 0androidx.compose.ui.graphics.drawscope.DrawScope  floatArrayOf 0androidx.compose.ui.graphics.drawscope.DrawScope  toPx 0androidx.compose.ui.graphics.drawscope.DrawScope  cap -androidx.compose.ui.graphics.drawscope.Stroke  width -androidx.compose.ui.graphics.drawscope.Stroke  copy "androidx.compose.ui.text.TextStyle  Bold (androidx.compose.ui.text.font.FontWeight  Bold 2androidx.compose.ui.text.font.FontWeight.Companion  	TextAlign androidx.compose.ui.text.style  Center (androidx.compose.ui.text.style.TextAlign  	Companion (androidx.compose.ui.text.style.TextAlign  Center 2androidx.compose.ui.text.style.TextAlign.Companion  toPx  androidx.compose.ui.unit.Density  toPx androidx.compose.ui.unit.Dp  FaceDetectionGuide #androidx.core.app.ComponentActivity  Canvas com.wendy.face  Column com.wendy.face  
ExifInterface com.wendy.face  FaceDetectionGuide com.wendy.face  
FontWeight com.wendy.face  
MaterialTheme com.wendy.face  
PathEffect com.wendy.face  Spacer com.wendy.face  	StrokeCap com.wendy.face  	TextAlign com.wendy.face  dashPathEffect com.wendy.face  floatArrayOf com.wendy.face  height com.wendy.face  loadAndRotateBitmap com.wendy.face  FaceDetectionGuide com.wendy.face.MainActivity  
FloatArray kotlin  floatArrayOf kotlin  
background androidx.compose.foundation  Box +androidx.compose.foundation.layout.BoxScope  Brush +androidx.compose.foundation.layout.BoxScope  Card +androidx.compose.foundation.layout.BoxScope  CardDefaults +androidx.compose.foundation.layout.BoxScope  CircleShape +androidx.compose.foundation.layout.BoxScope  FloatingActionButton +androidx.compose.foundation.layout.BoxScope  RoundedCornerShape +androidx.compose.foundation.layout.BoxScope  
background +androidx.compose.foundation.layout.BoxScope  
cardColors +androidx.compose.foundation.layout.BoxScope  linearGradient +androidx.compose.foundation.layout.BoxScope  listOf +androidx.compose.foundation.layout.BoxScope  radialGradient +androidx.compose.foundation.layout.BoxScope  shadow +androidx.compose.foundation.layout.BoxScope  size +androidx.compose.foundation.layout.BoxScope  
sweepGradient +androidx.compose.foundation.layout.BoxScope  verticalGradient +androidx.compose.foundation.layout.BoxScope  Arrangement .androidx.compose.foundation.layout.ColumnScope  Box .androidx.compose.foundation.layout.ColumnScope  Brush .androidx.compose.foundation.layout.ColumnScope  CircleShape .androidx.compose.foundation.layout.ColumnScope  Column .androidx.compose.foundation.layout.ColumnScope  FloatingActionButton .androidx.compose.foundation.layout.ColumnScope  Log .androidx.compose.foundation.layout.ColumnScope  Row .androidx.compose.foundation.layout.ColumnScope  androidx .androidx.compose.foundation.layout.ColumnScope  
background .androidx.compose.foundation.layout.ColumnScope  fillMaxSize .androidx.compose.foundation.layout.ColumnScope  fillMaxWidth .androidx.compose.foundation.layout.ColumnScope  let .androidx.compose.foundation.layout.ColumnScope  listOf .androidx.compose.foundation.layout.ColumnScope  padding .androidx.compose.foundation.layout.ColumnScope  radialGradient .androidx.compose.foundation.layout.ColumnScope  shadow .androidx.compose.foundation.layout.ColumnScope  size .androidx.compose.foundation.layout.ColumnScope  takePicture .androidx.compose.foundation.layout.ColumnScope  testFaceDetection .androidx.compose.foundation.layout.ColumnScope  Box +androidx.compose.foundation.layout.RowScope  Brush +androidx.compose.foundation.layout.RowScope  CircleShape +androidx.compose.foundation.layout.RowScope  Color +androidx.compose.foundation.layout.RowScope  FloatingActionButton +androidx.compose.foundation.layout.RowScope  
MaterialTheme +androidx.compose.foundation.layout.RowScope  Modifier +androidx.compose.foundation.layout.RowScope  Spacer +androidx.compose.foundation.layout.RowScope  androidx +androidx.compose.foundation.layout.RowScope  
background +androidx.compose.foundation.layout.RowScope  dp +androidx.compose.foundation.layout.RowScope  fillMaxSize +androidx.compose.foundation.layout.RowScope  listOf +androidx.compose.foundation.layout.RowScope  padding +androidx.compose.foundation.layout.RowScope  radialGradient +androidx.compose.foundation.layout.RowScope  shadow +androidx.compose.foundation.layout.RowScope  size +androidx.compose.foundation.layout.RowScope  CircleShape !androidx.compose.foundation.shape  RoundedCornerShape !androidx.compose.foundation.shape  Card androidx.compose.material3  
CardColors androidx.compose.material3  CardDefaults androidx.compose.material3  
cardColors 'androidx.compose.material3.CardDefaults  
bodyMedium %androidx.compose.material3.Typography  	bodySmall %androidx.compose.material3.Typography  
headlineLarge %androidx.compose.material3.Typography  Brush androidx.compose.runtime  Card androidx.compose.runtime  CardDefaults androidx.compose.runtime  CircleShape androidx.compose.runtime  FloatingActionButton androidx.compose.runtime  RoundedCornerShape androidx.compose.runtime  
background androidx.compose.runtime  
cardColors androidx.compose.runtime  linearGradient androidx.compose.runtime  radialGradient androidx.compose.runtime  shadow androidx.compose.runtime  size androidx.compose.runtime  
sweepGradient androidx.compose.runtime  verticalGradient androidx.compose.runtime  Center androidx.compose.ui.Alignment  CenterVertically androidx.compose.ui.Alignment  Vertical androidx.compose.ui.Alignment  Center 'androidx.compose.ui.Alignment.Companion  CenterVertically 'androidx.compose.ui.Alignment.Companion  
background androidx.compose.ui.Modifier  shadow androidx.compose.ui.Modifier  size androidx.compose.ui.Modifier  padding &androidx.compose.ui.Modifier.Companion  size &androidx.compose.ui.Modifier.Companion  clip androidx.compose.ui.draw  shadow androidx.compose.ui.draw  Brush androidx.compose.ui.graphics  	Companion "androidx.compose.ui.graphics.Brush  linearGradient "androidx.compose.ui.graphics.Brush  radialGradient "androidx.compose.ui.graphics.Brush  
sweepGradient "androidx.compose.ui.graphics.Brush  verticalGradient "androidx.compose.ui.graphics.Brush  linearGradient ,androidx.compose.ui.graphics.Brush.Companion  radialGradient ,androidx.compose.ui.graphics.Brush.Companion  
sweepGradient ,androidx.compose.ui.graphics.Brush.Companion  verticalGradient ,androidx.compose.ui.graphics.Brush.Companion  Transparent "androidx.compose.ui.graphics.Color  Transparent ,androidx.compose.ui.graphics.Color.Companion  Brush 0androidx.compose.ui.graphics.drawscope.DrawScope  linearGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  radialGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  
sweepGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  verticalGradient 0androidx.compose.ui.graphics.drawscope.DrawScope  Brush com.wendy.face  Card com.wendy.face  CardDefaults com.wendy.face  CircleShape com.wendy.face  FloatingActionButton com.wendy.face  RoundedCornerShape com.wendy.face  
background com.wendy.face  
cardColors com.wendy.face  linearGradient com.wendy.face  radialGradient com.wendy.face  shadow com.wendy.face  size com.wendy.face  
sweepGradient com.wendy.face  verticalGradient com.wendy.face  FaceLandmark 0androidx.compose.ui.graphics.drawscope.DrawScope  drawBoundingBox 0androidx.compose.ui.graphics.drawscope.DrawScope  
drawLandmarks 0androidx.compose.ui.graphics.drawscope.DrawScope  FaceLandmark com.google.mlkit.vision.face  close )com.google.mlkit.vision.face.FaceDetector  Bitmap com.wendy.face.camera  Boolean com.wendy.face.camera  
CameraManager com.wendy.face.camera  CameraSelector com.wendy.face.camera  Context com.wendy.face.camera  
ContextCompat com.wendy.face.camera  	Exception com.wendy.face.camera  FaceDetectorManager com.wendy.face.camera  ImageCapture com.wendy.face.camera  ImageCaptureException com.wendy.face.camera  
ImageUtils com.wendy.face.camera  Int com.wendy.face.camera  LifecycleOwner com.wendy.face.camera  List com.wendy.face.camera  Locale com.wendy.face.camera  Log com.wendy.face.camera  Preview com.wendy.face.camera  PreviewView com.wendy.face.camera  ProcessCameraProvider com.wendy.face.camera  SimpleDateFormat com.wendy.face.camera  System com.wendy.face.camera  TAG com.wendy.face.camera  Unit com.wendy.face.camera  Uri com.wendy.face.camera  also com.wendy.face.camera  android com.wendy.face.camera  apply com.wendy.face.camera  com com.wendy.face.camera  context com.wendy.face.camera  createTestBitmap com.wendy.face.camera  faceDetectorManager com.wendy.face.camera  let com.wendy.face.camera  loadAndRotateBitmap com.wendy.face.camera  Bitmap #com.wendy.face.camera.CameraManager  Boolean #com.wendy.face.camera.CameraManager  CameraSelector #com.wendy.face.camera.CameraManager  Context #com.wendy.face.camera.CameraManager  
ContextCompat #com.wendy.face.camera.CameraManager  	Exception #com.wendy.face.camera.CameraManager  FaceDetectorManager #com.wendy.face.camera.CameraManager  ImageCapture #com.wendy.face.camera.CameraManager  ImageCaptureException #com.wendy.face.camera.CameraManager  
ImageUtils #com.wendy.face.camera.CameraManager  Int #com.wendy.face.camera.CameraManager  LifecycleOwner #com.wendy.face.camera.CameraManager  List #com.wendy.face.camera.CameraManager  Locale #com.wendy.face.camera.CameraManager  Log #com.wendy.face.camera.CameraManager  Preview #com.wendy.face.camera.CameraManager  PreviewView #com.wendy.face.camera.CameraManager  ProcessCameraProvider #com.wendy.face.camera.CameraManager  SimpleDateFormat #com.wendy.face.camera.CameraManager  System #com.wendy.face.camera.CameraManager  TAG #com.wendy.face.camera.CameraManager  Unit #com.wendy.face.camera.CameraManager  Uri #com.wendy.face.camera.CameraManager  also #com.wendy.face.camera.CameraManager  android #com.wendy.face.camera.CameraManager  apply #com.wendy.face.camera.CameraManager  cameraProvider #com.wendy.face.camera.CameraManager  cameraProviderFuture #com.wendy.face.camera.CameraManager  com #com.wendy.face.camera.CameraManager  context #com.wendy.face.camera.CameraManager  createTestBitmap #com.wendy.face.camera.CameraManager  faceDetectorManager #com.wendy.face.camera.CameraManager  imageCapture #com.wendy.face.camera.CameraManager  let #com.wendy.face.camera.CameraManager  lifecycleOwner #com.wendy.face.camera.CameraManager  loadAndRotateBitmap #com.wendy.face.camera.CameraManager  CameraSelector -com.wendy.face.camera.CameraManager.Companion  
ContextCompat -com.wendy.face.camera.CameraManager.Companion  FaceDetectorManager -com.wendy.face.camera.CameraManager.Companion  ImageCapture -com.wendy.face.camera.CameraManager.Companion  
ImageUtils -com.wendy.face.camera.CameraManager.Companion  Locale -com.wendy.face.camera.CameraManager.Companion  Log -com.wendy.face.camera.CameraManager.Companion  Preview -com.wendy.face.camera.CameraManager.Companion  ProcessCameraProvider -com.wendy.face.camera.CameraManager.Companion  SimpleDateFormat -com.wendy.face.camera.CameraManager.Companion  System -com.wendy.face.camera.CameraManager.Companion  TAG -com.wendy.face.camera.CameraManager.Companion  also -com.wendy.face.camera.CameraManager.Companion  android -com.wendy.face.camera.CameraManager.Companion  apply -com.wendy.face.camera.CameraManager.Companion  context -com.wendy.face.camera.CameraManager.Companion  createTestBitmap -com.wendy.face.camera.CameraManager.Companion  faceDetectorManager -com.wendy.face.camera.CameraManager.Companion  let -com.wendy.face.camera.CameraManager.Companion  loadAndRotateBitmap -com.wendy.face.camera.CameraManager.Companion  OnImageSavedCallback 0com.wendy.face.camera.CameraManager.ImageCapture  OutputFileResults 0com.wendy.face.camera.CameraManager.ImageCapture  google 'com.wendy.face.camera.CameraManager.com  mlkit .com.wendy.face.camera.CameraManager.com.google  vision 4com.wendy.face.camera.CameraManager.com.google.mlkit  face ;com.wendy.face.camera.CameraManager.com.google.mlkit.vision  Face @com.wendy.face.camera.CameraManager.com.google.mlkit.vision.face  OnImageSavedCallback "com.wendy.face.camera.ImageCapture  OutputFileResults "com.wendy.face.camera.ImageCapture  google com.wendy.face.camera.com  mlkit  com.wendy.face.camera.com.google  vision &com.wendy.face.camera.com.google.mlkit  face -com.wendy.face.camera.com.google.mlkit.vision  Face 2com.wendy.face.camera.com.google.mlkit.vision.face  Bitmap com.wendy.face.detection  Color com.wendy.face.detection  	DrawScope com.wendy.face.detection  	Exception com.wendy.face.detection  Face com.wendy.face.detection  FaceContour com.wendy.face.detection  
FaceDetection com.wendy.face.detection  FaceDetectorManager com.wendy.face.detection  FaceDetectorOptions com.wendy.face.detection  FaceLandmark com.wendy.face.detection  FaceRenderer com.wendy.face.detection  Float com.wendy.face.detection  
InputImage com.wendy.face.detection  Int com.wendy.face.detection  List com.wendy.face.detection  Log com.wendy.face.detection  Path com.wendy.face.detection  Stroke com.wendy.face.detection  TAG com.wendy.face.detection  Unit com.wendy.face.detection  androidx com.wendy.face.detection  	emptyList com.wendy.face.detection  forEach com.wendy.face.detection  forEachIndexed com.wendy.face.detection  let com.wendy.face.detection  listOf com.wendy.face.detection  until com.wendy.face.detection  with com.wendy.face.detection  Bitmap ,com.wendy.face.detection.FaceDetectorManager  	Exception ,com.wendy.face.detection.FaceDetectorManager  Face ,com.wendy.face.detection.FaceDetectorManager  
FaceDetection ,com.wendy.face.detection.FaceDetectorManager  FaceDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  
InputImage ,com.wendy.face.detection.FaceDetectorManager  Int ,com.wendy.face.detection.FaceDetectorManager  List ,com.wendy.face.detection.FaceDetectorManager  Log ,com.wendy.face.detection.FaceDetectorManager  TAG ,com.wendy.face.detection.FaceDetectorManager  Unit ,com.wendy.face.detection.FaceDetectorManager  detectFaces ,com.wendy.face.detection.FaceDetectorManager  	emptyList ,com.wendy.face.detection.FaceDetectorManager  faceDetector ,com.wendy.face.detection.FaceDetectorManager  faceDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  forEachIndexed ,com.wendy.face.detection.FaceDetectorManager  release ,com.wendy.face.detection.FaceDetectorManager  testFaceDetection ,com.wendy.face.detection.FaceDetectorManager  
FaceDetection 6com.wendy.face.detection.FaceDetectorManager.Companion  FaceDetectorOptions 6com.wendy.face.detection.FaceDetectorManager.Companion  
InputImage 6com.wendy.face.detection.FaceDetectorManager.Companion  Log 6com.wendy.face.detection.FaceDetectorManager.Companion  TAG 6com.wendy.face.detection.FaceDetectorManager.Companion  	emptyList 6com.wendy.face.detection.FaceDetectorManager.Companion  forEachIndexed 6com.wendy.face.detection.FaceDetectorManager.Companion  Color %com.wendy.face.detection.FaceRenderer  FaceContour %com.wendy.face.detection.FaceRenderer  FaceLandmark %com.wendy.face.detection.FaceRenderer  Path %com.wendy.face.detection.FaceRenderer  Stroke %com.wendy.face.detection.FaceRenderer  androidx %com.wendy.face.detection.FaceRenderer  dp %com.wendy.face.detection.FaceRenderer  drawBoundingBox %com.wendy.face.detection.FaceRenderer  drawFaceContours %com.wendy.face.detection.FaceRenderer  
drawLandmarks %com.wendy.face.detection.FaceRenderer  let %com.wendy.face.detection.FaceRenderer  listOf %com.wendy.face.detection.FaceRenderer  until %com.wendy.face.detection.FaceRenderer  with %com.wendy.face.detection.FaceRenderer  Array com.wendy.face.utils  Bitmap com.wendy.face.utils  
BitmapFactory com.wendy.face.utils  Boolean com.wendy.face.utils  Context com.wendy.face.utils  
ContextCompat com.wendy.face.utils  	Exception com.wendy.face.utils  
ExifInterface com.wendy.face.utils  Float com.wendy.face.utils  
ImageUtils com.wendy.face.utils  Int com.wendy.face.utils  Log com.wendy.face.utils  Manifest com.wendy.face.utils  Matrix com.wendy.face.utils  PackageManager com.wendy.face.utils  PermissionUtils com.wendy.face.utils  String com.wendy.face.utils  Uri com.wendy.face.utils  android com.wendy.face.utils  apply com.wendy.face.utils  arrayOf com.wendy.face.utils  
mutableListOf com.wendy.face.utils  toTypedArray com.wendy.face.utils  Bitmap com.wendy.face.utils.ImageUtils  
BitmapFactory com.wendy.face.utils.ImageUtils  
ExifInterface com.wendy.face.utils.ImageUtils  Log com.wendy.face.utils.ImageUtils  Matrix com.wendy.face.utils.ImageUtils  TAG com.wendy.face.utils.ImageUtils  android com.wendy.face.utils.ImageUtils  apply com.wendy.face.utils.ImageUtils  arrayOf com.wendy.face.utils.ImageUtils  createTestBitmap com.wendy.face.utils.ImageUtils  loadAndRotateBitmap com.wendy.face.utils.ImageUtils  
ContextCompat $com.wendy.face.utils.PermissionUtils  Manifest $com.wendy.face.utils.PermissionUtils  PackageManager $com.wendy.face.utils.PermissionUtils  android $com.wendy.face.utils.PermissionUtils  hasCameraPermission $com.wendy.face.utils.PermissionUtils  hasStoragePermission $com.wendy.face.utils.PermissionUtils  
mutableListOf $com.wendy.face.utils.PermissionUtils  toTypedArray $com.wendy.face.utils.PermissionUtils  with kotlin  invoke kotlin.Function1  FaceDetectionApp android.app.Activity  PermissionUtils android.app.Activity  getPermissionsToRequest android.app.Activity  FaceDetectionApp android.content.Context  PermissionUtils android.content.Context  getPermissionsToRequest android.content.Context  FaceDetectionApp android.content.ContextWrapper  PermissionUtils android.content.ContextWrapper  getPermissionsToRequest android.content.ContextWrapper  FaceDetectionApp  android.view.ContextThemeWrapper  PermissionUtils  android.view.ContextThemeWrapper  getPermissionsToRequest  android.view.ContextThemeWrapper  FaceDetectionApp #androidx.activity.ComponentActivity  PermissionUtils #androidx.activity.ComponentActivity  getPermissionsToRequest #androidx.activity.ComponentActivity  ActivityResultContracts "androidx.compose.foundation.layout  	Alignment "androidx.compose.foundation.layout  Bitmap "androidx.compose.foundation.layout  Brush "androidx.compose.foundation.layout  Bundle "androidx.compose.foundation.layout  Button "androidx.compose.foundation.layout  
CameraView "androidx.compose.foundation.layout  Canvas "androidx.compose.foundation.layout  Card "androidx.compose.foundation.layout  CardDefaults "androidx.compose.foundation.layout  CircleShape "androidx.compose.foundation.layout  Color "androidx.compose.foundation.layout  ComponentActivity "androidx.compose.foundation.layout  
Composable "androidx.compose.foundation.layout  ContentScale "androidx.compose.foundation.layout  Face "androidx.compose.foundation.layout  FaceDetectionApp "androidx.compose.foundation.layout  FaceOverlay "androidx.compose.foundation.layout  	FaceTheme "androidx.compose.foundation.layout  FloatingActionButton "androidx.compose.foundation.layout  
FontWeight "androidx.compose.foundation.layout  Image "androidx.compose.foundation.layout  List "androidx.compose.foundation.layout  Log "androidx.compose.foundation.layout  
MaterialTheme "androidx.compose.foundation.layout  Modifier "androidx.compose.foundation.layout  Offset "androidx.compose.foundation.layout  
PathEffect "androidx.compose.foundation.layout  PermissionUtils "androidx.compose.foundation.layout  RoundedCornerShape "androidx.compose.foundation.layout  Size "androidx.compose.foundation.layout  Stroke "androidx.compose.foundation.layout  	StrokeCap "androidx.compose.foundation.layout  Text "androidx.compose.foundation.layout  	TextAlign "androidx.compose.foundation.layout  Unit "androidx.compose.foundation.layout  Uri "androidx.compose.foundation.layout  align "androidx.compose.foundation.layout  androidx "androidx.compose.foundation.layout  
asImageBitmap "androidx.compose.foundation.layout  
background "androidx.compose.foundation.layout  
cardColors "androidx.compose.foundation.layout  dashPathEffect "androidx.compose.foundation.layout  	emptyList "androidx.compose.foundation.layout  floatArrayOf "androidx.compose.foundation.layout  forEach "androidx.compose.foundation.layout  getPermissionsToRequest "androidx.compose.foundation.layout  getValue "androidx.compose.foundation.layout  
isNotEmpty "androidx.compose.foundation.layout  let "androidx.compose.foundation.layout  linearGradient "androidx.compose.foundation.layout  listOf "androidx.compose.foundation.layout  mutableStateOf "androidx.compose.foundation.layout  provideDelegate "androidx.compose.foundation.layout  radialGradient "androidx.compose.foundation.layout  remember "androidx.compose.foundation.layout  setValue "androidx.compose.foundation.layout  shadow "androidx.compose.foundation.layout  
sweepGradient "androidx.compose.foundation.layout  verticalGradient "androidx.compose.foundation.layout  	Alignment +androidx.compose.foundation.layout.BoxScope  CameraControls +androidx.compose.foundation.layout.BoxScope  Size +androidx.compose.foundation.layout.BoxScope  	Alignment .androidx.compose.foundation.layout.ColumnScope  	Alignment +androidx.compose.foundation.layout.RowScope  	Alignment androidx.compose.material3  Arrangement androidx.compose.material3  Box androidx.compose.material3  Brush androidx.compose.material3  Canvas androidx.compose.material3  CircleShape androidx.compose.material3  Color androidx.compose.material3  Column androidx.compose.material3  
Composable androidx.compose.material3  
FontWeight androidx.compose.material3  Log androidx.compose.material3  Modifier androidx.compose.material3  Offset androidx.compose.material3  
PathEffect androidx.compose.material3  RoundedCornerShape androidx.compose.material3  Row androidx.compose.material3  Size androidx.compose.material3  Spacer androidx.compose.material3  Stroke androidx.compose.material3  	StrokeCap androidx.compose.material3  	TextAlign androidx.compose.material3  Unit androidx.compose.material3  align androidx.compose.material3  
background androidx.compose.material3  
cardColors androidx.compose.material3  dashPathEffect androidx.compose.material3  fillMaxSize androidx.compose.material3  fillMaxWidth androidx.compose.material3  floatArrayOf androidx.compose.material3  height androidx.compose.material3  linearGradient androidx.compose.material3  listOf androidx.compose.material3  padding androidx.compose.material3  radialGradient androidx.compose.material3  shadow androidx.compose.material3  size androidx.compose.material3  
sweepGradient androidx.compose.material3  verticalGradient androidx.compose.material3  	Alignment androidx.compose.runtime  CameraControls androidx.compose.runtime  DisposableEffect androidx.compose.runtime  DisposableEffectResult androidx.compose.runtime  DisposableEffectScope androidx.compose.runtime  FaceDetectionApp androidx.compose.runtime  PermissionUtils androidx.compose.runtime  getPermissionsToRequest androidx.compose.runtime  	onDispose .androidx.compose.runtime.DisposableEffectScope  	Alignment androidx.compose.ui.graphics  Box androidx.compose.ui.graphics  Canvas androidx.compose.ui.graphics  Card androidx.compose.ui.graphics  CardDefaults androidx.compose.ui.graphics  CircleShape androidx.compose.ui.graphics  Column androidx.compose.ui.graphics  
Composable androidx.compose.ui.graphics  
FontWeight androidx.compose.ui.graphics  
MaterialTheme androidx.compose.ui.graphics  Modifier androidx.compose.ui.graphics  Offset androidx.compose.ui.graphics  RoundedCornerShape androidx.compose.ui.graphics  Row androidx.compose.ui.graphics  Size androidx.compose.ui.graphics  Spacer androidx.compose.ui.graphics  Stroke androidx.compose.ui.graphics  Text androidx.compose.ui.graphics  	TextAlign androidx.compose.ui.graphics  align androidx.compose.ui.graphics  
background androidx.compose.ui.graphics  
cardColors androidx.compose.ui.graphics  dashPathEffect androidx.compose.ui.graphics  fillMaxSize androidx.compose.ui.graphics  floatArrayOf androidx.compose.ui.graphics  height androidx.compose.ui.graphics  linearGradient androidx.compose.ui.graphics  listOf androidx.compose.ui.graphics  padding androidx.compose.ui.graphics  radialGradient androidx.compose.ui.graphics  shadow androidx.compose.ui.graphics  size androidx.compose.ui.graphics  
sweepGradient androidx.compose.ui.graphics  verticalGradient androidx.compose.ui.graphics  FaceRenderer 0androidx.compose.ui.graphics.drawscope.DrawScope  
ImageUtils 0androidx.compose.ui.graphics.drawscope.DrawScope  Size 0androidx.compose.ui.graphics.drawscope.DrawScope  calculateDisplayBounds 0androidx.compose.ui.graphics.drawscope.DrawScope  	drawFaces 0androidx.compose.ui.graphics.drawscope.DrawScope  FaceDetectionApp #androidx.core.app.ComponentActivity  PermissionUtils #androidx.core.app.ComponentActivity  getPermissionsToRequest #androidx.core.app.ComponentActivity  	Alignment com.wendy.face  FaceDetectionApp com.wendy.face  PermissionUtils com.wendy.face  getPermissionsToRequest com.wendy.face  FaceDetectionApp com.wendy.face.MainActivity  PermissionUtils com.wendy.face.MainActivity  getPermissionsToRequest com.wendy.face.MainActivity  
bindCamera #com.wendy.face.camera.CameraManager  performTestDetection #com.wendy.face.camera.CameraManager  release #com.wendy.face.camera.CameraManager  takePictureAndDetect #com.wendy.face.camera.CameraManager  Offset com.wendy.face.detection  Size com.wendy.face.detection  Offset %com.wendy.face.detection.FaceRenderer  Size %com.wendy.face.detection.FaceRenderer  	drawFaces %com.wendy.face.detection.FaceRenderer  	Alignment com.wendy.face.ui.components  AndroidView com.wendy.face.ui.components  Arrangement com.wendy.face.ui.components  Bitmap com.wendy.face.ui.components  Boolean com.wendy.face.ui.components  Box com.wendy.face.ui.components  Brush com.wendy.face.ui.components  CameraControls com.wendy.face.ui.components  
CameraView com.wendy.face.ui.components  Canvas com.wendy.face.ui.components  Card com.wendy.face.ui.components  CardDefaults com.wendy.face.ui.components  CircleShape com.wendy.face.ui.components  Color com.wendy.face.ui.components  Column com.wendy.face.ui.components  
Composable com.wendy.face.ui.components  DisposableEffect com.wendy.face.ui.components  Face com.wendy.face.ui.components  FaceDetectionGuide com.wendy.face.ui.components  FaceOverlay com.wendy.face.ui.components  FaceRenderer com.wendy.face.ui.components  FloatingActionButton com.wendy.face.ui.components  
FontWeight com.wendy.face.ui.components  
ImageUtils com.wendy.face.ui.components  Int com.wendy.face.ui.components  List com.wendy.face.ui.components  Log com.wendy.face.ui.components  
MaterialTheme com.wendy.face.ui.components  Modifier com.wendy.face.ui.components  Offset com.wendy.face.ui.components  
PathEffect com.wendy.face.ui.components  PreviewView com.wendy.face.ui.components  RoundedCornerShape com.wendy.face.ui.components  Row com.wendy.face.ui.components  Size com.wendy.face.ui.components  Spacer com.wendy.face.ui.components  Stroke com.wendy.face.ui.components  	StrokeCap com.wendy.face.ui.components  Text com.wendy.face.ui.components  	TextAlign com.wendy.face.ui.components  Unit com.wendy.face.ui.components  Uri com.wendy.face.ui.components  align com.wendy.face.ui.components  
background com.wendy.face.ui.components  calculateDisplayBounds com.wendy.face.ui.components  
cardColors com.wendy.face.ui.components  dashPathEffect com.wendy.face.ui.components  	drawFaces com.wendy.face.ui.components  fillMaxSize com.wendy.face.ui.components  fillMaxWidth com.wendy.face.ui.components  floatArrayOf com.wendy.face.ui.components  getValue com.wendy.face.ui.components  height com.wendy.face.ui.components  linearGradient com.wendy.face.ui.components  listOf com.wendy.face.ui.components  mutableStateOf com.wendy.face.ui.components  padding com.wendy.face.ui.components  provideDelegate com.wendy.face.ui.components  radialGradient com.wendy.face.ui.components  remember com.wendy.face.ui.components  setValue com.wendy.face.ui.components  shadow com.wendy.face.ui.components  size com.wendy.face.ui.components  
sweepGradient com.wendy.face.ui.components  verticalGradient com.wendy.face.ui.components  calculateDisplayBounds com.wendy.face.utils.ImageUtils  getPermissionsToRequest $com.wendy.face.utils.PermissionUtils  get kotlin.Array  
isNotEmpty kotlin.Array  
ImageUtils "androidx.compose.foundation.layout  cropHeadRegion "androidx.compose.foundation.layout  spacedBy "androidx.compose.foundation.layout  
ImageUtils +androidx.compose.foundation.layout.BoxScope  cropHeadRegion +androidx.compose.foundation.layout.BoxScope  
isNotEmpty +androidx.compose.foundation.layout.BoxScope  Button .androidx.compose.foundation.layout.ColumnScope  
ImageUtils .androidx.compose.foundation.layout.ColumnScope  cropHeadRegion .androidx.compose.foundation.layout.ColumnScope  	emptyList .androidx.compose.foundation.layout.ColumnScope  
isNotEmpty .androidx.compose.foundation.layout.ColumnScope  
ImageUtils androidx.compose.runtime  cropHeadRegion androidx.compose.runtime  
ImageUtils com.wendy.face  cropHeadRegion com.wendy.face  Face com.wendy.face.utils  List com.wendy.face.utils  max com.wendy.face.utils  min com.wendy.face.utils  Int com.wendy.face.utils.ImageUtils  cropHeadRegion com.wendy.face.utils.ImageUtils  max com.wendy.face.utils.ImageUtils  min com.wendy.face.utils.ImageUtils  Int kotlin  toInt kotlin.Float  	Companion 
kotlin.Int  	MAX_VALUE 
kotlin.Int  	MIN_VALUE 
kotlin.Int  minus 
kotlin.Int  plus 
kotlin.Int  	MAX_VALUE kotlin.Int.Companion  	MIN_VALUE kotlin.Int.Companion  max kotlin.collections  min kotlin.collections  
isNotEmpty kotlin.collections.List  max kotlin.math  min kotlin.math  max kotlin.sequences  min kotlin.sequences  max kotlin.text  min kotlin.text  toInt 
kotlin.Double  div 
kotlin.Int  height android.graphics.Rect  width android.graphics.Rect  com "androidx.compose.foundation.layout  first com.wendy.face.utils  first com.wendy.face.utils.ImageUtils  first kotlin.collections  first kotlin.collections.List  size kotlin.collections.List  first 
kotlin.ranges  
KProperty0 kotlin.reflect  first kotlin.sequences  first kotlin.text  SuppressLint android.annotation  Bitmap androidx.camera.core  Boolean androidx.camera.core  Context androidx.camera.core  
ContextCompat androidx.camera.core  	Exception androidx.camera.core  	Executors androidx.camera.core  FaceDetectorManager androidx.camera.core  FaceMesh androidx.camera.core  
ImageUtils androidx.camera.core  
InputImage androidx.camera.core  Int androidx.camera.core  LifecycleOwner androidx.camera.core  List androidx.camera.core  Locale androidx.camera.core  Log androidx.camera.core  PreviewView androidx.camera.core  ProcessCameraProvider androidx.camera.core  SimpleDateFormat androidx.camera.core  SuppressLint androidx.camera.core  System androidx.camera.core  TAG androidx.camera.core  Unit androidx.camera.core  Uri androidx.camera.core  also androidx.camera.core  android androidx.camera.core  apply androidx.camera.core  context androidx.camera.core  let androidx.camera.core  loadAndRotateBitmap androidx.camera.core  FaceMesh "androidx.compose.foundation.layout  map "androidx.compose.foundation.layout  map +androidx.compose.foundation.layout.BoxScope  map .androidx.compose.foundation.layout.ColumnScope  FaceMesh androidx.compose.runtime  map androidx.compose.runtime  drawFaceMeshPoints 0androidx.compose.ui.graphics.drawscope.DrawScope  drawFaceMeshes 0androidx.compose.ui.graphics.drawscope.DrawScope  bitmapInternal )com.google.mlkit.vision.common.InputImage  x 'com.google.mlkit.vision.common.PointF3D  y 'com.google.mlkit.vision.common.PointF3D  FaceMesh  com.google.mlkit.vision.facemesh  FaceMeshDetection  com.google.mlkit.vision.facemesh  FaceMeshDetector  com.google.mlkit.vision.facemesh  FaceMeshDetectorOptions  com.google.mlkit.vision.facemesh  	allPoints )com.google.mlkit.vision.facemesh.FaceMesh  boundingBox )com.google.mlkit.vision.facemesh.FaceMesh  	getClient 2com.google.mlkit.vision.facemesh.FaceMeshDetection  close 1com.google.mlkit.vision.facemesh.FaceMeshDetector  process 1com.google.mlkit.vision.facemesh.FaceMeshDetector  Builder 8com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions  	FACE_MESH 8com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions  build @com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions.Builder  
setUseCase @com.google.mlkit.vision.facemesh.FaceMeshDetectorOptions.Builder  position .com.google.mlkit.vision.facemesh.FaceMeshPoint  FaceMesh com.wendy.face  map com.wendy.face  	Executors com.wendy.face.camera  FaceMesh com.wendy.face.camera  
ImageAnalysis com.wendy.face.camera  
InputImage com.wendy.face.camera  SuppressLint com.wendy.face.camera  	Executors #com.wendy.face.camera.CameraManager  FaceMesh #com.wendy.face.camera.CameraManager  
ImageAnalysis #com.wendy.face.camera.CameraManager  
InputImage #com.wendy.face.camera.CameraManager  SuppressLint #com.wendy.face.camera.CameraManager  cameraExecutor #com.wendy.face.camera.CameraManager  takePicture #com.wendy.face.camera.CameraManager  	Executors -com.wendy.face.camera.CameraManager.Companion  
ImageAnalysis -com.wendy.face.camera.CameraManager.Companion  
InputImage -com.wendy.face.camera.CameraManager.Companion  FaceMesh com.wendy.face.detection  FaceMeshDetection com.wendy.face.detection  FaceMeshDetectorOptions com.wendy.face.detection  FaceMesh ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetection ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  faceMeshDetector ,com.wendy.face.detection.FaceDetectorManager  faceMeshDetectorOptions ,com.wendy.face.detection.FaceDetectorManager  FaceMeshDetection 6com.wendy.face.detection.FaceDetectorManager.Companion  FaceMeshDetectorOptions 6com.wendy.face.detection.FaceDetectorManager.Companion  drawFaceMeshPoints %com.wendy.face.detection.FaceRenderer  drawFaceMeshes %com.wendy.face.detection.FaceRenderer  FaceMesh com.wendy.face.ui.components  drawFaceMeshes com.wendy.face.ui.components  Rect com.wendy.face.utils  Bitmap 	java.util  Boolean 	java.util  CameraSelector 	java.util  Context 	java.util  
ContextCompat 	java.util  	Exception 	java.util  	Executors 	java.util  FaceDetectorManager 	java.util  FaceMesh 	java.util  
ImageAnalysis 	java.util  ImageCapture 	java.util  ImageCaptureException 	java.util  
ImageUtils 	java.util  
InputImage 	java.util  Int 	java.util  LifecycleOwner 	java.util  List 	java.util  Log 	java.util  Preview 	java.util  PreviewView 	java.util  ProcessCameraProvider 	java.util  SimpleDateFormat 	java.util  SuppressLint 	java.util  System 	java.util  TAG 	java.util  Unit 	java.util  Uri 	java.util  also 	java.util  android 	java.util  apply 	java.util  context 	java.util  let 	java.util  loadAndRotateBitmap 	java.util  OnImageSavedCallback java.util.ImageCapture  OutputFileResults java.util.ImageCapture  shutdown $java.util.concurrent.ExecutorService  Result kotlin  map kotlin  map kotlin.collections  map kotlin.collections.List  Sequence kotlin.sequences  map kotlin.sequences  map kotlin.text  
InputImage "androidx.compose.foundation.layout  
InputImage +androidx.compose.foundation.layout.BoxScope  Boolean com.wendy.face.detection                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                        