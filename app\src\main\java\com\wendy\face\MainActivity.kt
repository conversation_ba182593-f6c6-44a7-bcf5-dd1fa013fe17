package com.wendy.face

import android.graphics.Bitmap
import android.net.Uri
import android.os.Build
import android.os.Bundle
import android.util.Log
import android.view.View
import android.view.WindowInsets
import android.view.WindowInsetsController
import android.view.WindowManager
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.view.WindowCompat
import androidx.compose.foundation.Image
import androidx.compose.foundation.layout.*
import androidx.compose.material3.Button
import androidx.compose.material3.Text
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.unit.dp
import com.google.mlkit.vision.common.InputImage
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.ui.components.CameraView
import com.wendy.face.ui.components.FaceOverlay
import com.wendy.face.ui.theme.FaceTheme
import com.wendy.face.utils.ImageUtils
import com.wendy.face.utils.PermissionUtils

/**
 * 主Activity - 重构后的简化版本
 * 负责权限管理和UI状态协调
 */
class MainActivity : ComponentActivity() {

    // 权限请求启动器
    private val requestMultiplePermissionsLauncher =
        registerForActivityResult(ActivityResultContracts.RequestMultiplePermissions()) { permissions ->
            permissions.entries.forEach { entry ->
                val permission = entry.key
                val isGranted = entry.value
                if (isGranted) {
                    Log.d("MainActivity", "Permission granted: $permission")
                } else {
                    Log.w("MainActivity", "Permission denied: $permission")
                }
            }
        }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 设置全面屏模式
        setupFullScreenMode()

        // 检查并请求权限
        val permissionsToRequest = PermissionUtils.getPermissionsToRequest(this)
        if (permissionsToRequest.isNotEmpty()) {
            requestMultiplePermissionsLauncher.launch(permissionsToRequest)
        }

        setContent {
            FaceTheme {
                FaceDetectionApp()
            }
        }
    }

    /**
     * 设置全面屏模式，隐藏状态栏和导航栏
     */
    private fun setupFullScreenMode() {
        try {
            // 启用边到边显示
            WindowCompat.setDecorFitsSystemWindows(window, false)

            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                // Android 11+ 使用新的WindowInsetsController
                window.insetsController?.let { controller ->
                    controller.hide(WindowInsets.Type.statusBars() or WindowInsets.Type.navigationBars())
                    controller.systemBarsBehavior = WindowInsetsController.BEHAVIOR_SHOW_TRANSIENT_BARS_BY_SWIPE
                }
            } else {
                // Android 10及以下版本
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                    or View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                )
            }

            // 设置窗口标志
            window.addFlags(WindowManager.LayoutParams.FLAG_KEEP_SCREEN_ON)
        } catch (e: Exception) {
            Log.e("MainActivity", "Failed to setup full screen mode", e)
            // 如果全面屏设置失败，至少隐藏状态栏
            try {
                @Suppress("DEPRECATION")
                window.decorView.systemUiVisibility = View.SYSTEM_UI_FLAG_FULLSCREEN
            } catch (ex: Exception) {
                Log.e("MainActivity", "Failed to hide status bar", ex)
            }
        }
    }
}

/**
 * 人脸检测应用主界面
 */
@Composable
fun FaceDetectionApp() {
    // 状态管理
    var faceMeshes by remember { mutableStateOf<List<FaceMesh>>(emptyList()) }
    var imageWidth by remember { mutableStateOf(0) }
    var imageHeight by remember { mutableStateOf(0) }
    var isBackCamera by remember { mutableStateOf(false) }
    var capturedImageUri by remember { mutableStateOf<Uri?>(null) }
    var capturedBitmap by remember { mutableStateOf<Bitmap?>(null) }
    var capturedFaceMeshes by remember { mutableStateOf<List<FaceMesh>>(emptyList()) }
    var capturedImageWidth by remember { mutableStateOf(0) }
    var capturedImageHeight by remember { mutableStateOf(0) }
    var showCamera by remember { mutableStateOf(true) }
    val faceDetectorManager by remember { mutableStateOf(com.wendy.face.detection.FaceDetectorManager()) }
    var showCroppedImage by remember { mutableStateOf(false) }
    var croppedBitmap by remember { mutableStateOf<Bitmap?>(null) }

    Box(modifier = Modifier.fillMaxSize()) {
        if (showCamera) {
            // 显示相机预览界面
            CameraView(
                isBackCamera = isBackCamera,
                onFacesDetected = { detectedFaceMeshes, width, height ->
                    faceMeshes = detectedFaceMeshes
                    imageWidth = width
                    imageHeight = height
                },
                onCameraSwitch = { isBackCamera = !isBackCamera },
                onImageCaptured = { uri, bitmap ->
                    capturedImageUri = uri
                    capturedBitmap = bitmap
                    showCamera = false // 先隐藏相机

                    // 对捕获的高清图进行人脸识别
                    bitmap?.let { bmp ->
                        val inputImage = InputImage.fromBitmap(bmp, 0)
                        faceDetectorManager.detectFaces(
                            inputImage = inputImage,
                            onSuccess = { detectedFaceMeshes, width, height ->
                                // 使用新检测到的人脸数据
                                capturedFaceMeshes = detectedFaceMeshes
                                capturedImageWidth = width
                                capturedImageHeight = height
                                Log.d("FaceDetectionApp", "New face meshes detected on captured image: ${detectedFaceMeshes.size}")
                            },
                            onFailure = { e ->
                                Log.e("FaceDetectionApp", "Face mesh detection on captured image failed", e)
                                // 即使失败，也显示图片，只是没有标注
                                capturedFaceMeshes = emptyList()
                                capturedImageWidth = bmp.width
                                capturedImageHeight = bmp.height
                            }
                        )
                    }
                }
            )
        } else {
            // 显示拍摄的照片或裁剪后的照片
            val displayBitmap = if (showCroppedImage) croppedBitmap else capturedBitmap
            displayBitmap?.let { bitmap ->
                Image(
                    bitmap = bitmap.asImageBitmap(),
                    contentDescription = if (showCroppedImage) "Cropped Photo" else "Captured Photo",
                    modifier = Modifier.fillMaxSize(),
                    contentScale = ContentScale.Fit
                )
            }

            // 控制按钮组
            Column(
                modifier = Modifier
                    .align(Alignment.TopEnd)
                    .padding(16.dp),
                verticalArrangement = Arrangement.spacedBy(8.dp)
            ) {
                // 返回摄像头按钮
                Button(
                    onClick = {
                        showCamera = true
                        faceMeshes = emptyList()
                        capturedImageUri = null
                        capturedBitmap = null
                        capturedFaceMeshes = emptyList()
                        capturedImageWidth = 0
                        capturedImageHeight = 0
                        showCroppedImage = false
                        croppedBitmap = null
                    }
                ) {
                    Text("返回摄像头")
                }

                // 裁剪头部按钮
                if (!showCroppedImage && capturedBitmap != null && capturedFaceMeshes.isNotEmpty()) {
                    Button(
                        onClick = {
                            capturedBitmap?.let { bitmap ->
                                // 注意：cropHeadRegion需要FaceMesh支持，这里假设它能处理
                                // 可能需要修改ImageUtils.cropHeadRegion来接受FaceMesh
                                val facesToCrop = capturedFaceMeshes.map { it.boundingBox }
                                croppedBitmap = ImageUtils.cropHeadRegion(bitmap, facesToCrop)
                                showCroppedImage = true
                            }
                        }
                    ) {
                        Text("裁剪头部")
                    }
                }

                // 显示原图按钮
                if (showCroppedImage) {
                    Button(
                        onClick = {
                            showCroppedImage = false
                        }
                    ) {
                        Text("显示原图")
                    }
                }
            }
        }

        // 人脸检测覆盖层
        if (showCamera) {
            FaceOverlay(faceMeshes = faceMeshes, imageWidth = imageWidth, imageHeight = imageHeight, isBackCamera = isBackCamera)
        } else if (!showCroppedImage) {
            // 只在显示原图时显示人脸检测结果，裁剪后的图片不显示覆盖层
            // 拍摄后的照片没有镜像，所以isBackCamera为true
            FaceOverlay(faceMeshes = capturedFaceMeshes, imageWidth = capturedImageWidth, imageHeight = capturedImageHeight, isBackCamera = true)
        }
    }
}
