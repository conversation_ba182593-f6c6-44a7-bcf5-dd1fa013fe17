package com.wendy.face.ui.components

import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.shadow
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.geometry.Size
import androidx.compose.ui.graphics.*
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.unit.dp

/**
 * 人脸检测引导组件
 * 显示检测框和提示信息
 */
@Composable
fun FaceDetectionGuide() {
    Box(modifier = Modifier.fillMaxSize()) {
        // 渐变背景遮罩 - 新氧风格
        Canvas(modifier = Modifier.fillMaxSize()) {
            val viewWidth = size.width
            val viewHeight = size.height
            
            // 计算检测框的位置和大小 - 增大尺寸
            val frameWidth = viewWidth * 0.85f  // 从0.7f增加到0.85f
            val frameHeight = frameWidth * 1.3f // 椭圆形，稍微高一些，从1.2f增加到1.3f
            val centerX = viewWidth / 2f
            val centerY = viewHeight / 2f - 30.dp.toPx() // 稍微向上偏移，减少偏移量
            
            val left = centerX - frameWidth / 2f
            val top = centerY - frameHeight / 2f
            val right = centerX + frameWidth / 2f
            val bottom = centerY + frameHeight / 2f
            
            // 绘制渐变遮罩背景（除了检测框区域）- 新氧风格
            val maskBrush = Brush.radialGradient(
                colors = listOf(
                    Color.Black.copy(alpha = 0.3f),
                    Color.Black.copy(alpha = 0.7f)
                ),
                center = Offset(centerX, centerY),
                radius = frameWidth * 0.8f
            )
            
            // 上方区域
            drawRect(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.Black.copy(alpha = 0.8f),
                        Color.Black.copy(alpha = 0.4f)
                    )
                ),
                topLeft = Offset(0f, 0f),
                size = Size(viewWidth, top)
            )
            // 下方区域
            drawRect(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.Black.copy(alpha = 0.4f),
                        Color.Black.copy(alpha = 0.8f)
                    )
                ),
                topLeft = Offset(0f, bottom),
                size = Size(viewWidth, viewHeight - bottom)
            )
            // 左侧区域
            drawRect(
                color = Color.Black.copy(alpha = 0.6f),
                topLeft = Offset(0f, top),
                size = Size(left, frameHeight)
            )
            // 右侧区域
            drawRect(
                color = Color.Black.copy(alpha = 0.6f),
                topLeft = Offset(right, top),
                size = Size(viewWidth - right, frameHeight)
            )
            
            // 绘制主检测框边框（椭圆形）- 新氧风格双层设计
            // 外层发光效果
            drawOval(
                color = Color.Cyan.copy(alpha = 0.3f),
                topLeft = Offset(left - 4.dp.toPx(), top - 4.dp.toPx()),
                size = Size(frameWidth + 8.dp.toPx(), frameHeight + 8.dp.toPx()),
                style = Stroke(width = 8.dp.toPx())
            )

            // 主检测框
            drawOval(
                brush = Brush.sweepGradient(
                    colors = listOf(
                        Color.Cyan,
                        Color.Blue,
                        Color.Magenta,
                        Color.Cyan
                    ),
                    center = Offset(centerX, centerY)
                ),
                topLeft = Offset(left, top),
                size = Size(frameWidth, frameHeight),
                style = Stroke(
                    width = 4.dp.toPx(),
                    pathEffect = PathEffect.dashPathEffect(floatArrayOf(25f, 15f), 0f)
                )
            )
            
            // 内层精细线条
            drawOval(
                color = Color.White.copy(alpha = 0.8f),
                topLeft = Offset(left + 2.dp.toPx(), top + 2.dp.toPx()),
                size = Size(frameWidth - 4.dp.toPx(), frameHeight - 4.dp.toPx()),
                style = Stroke(width = 1.dp.toPx())
            )
            
            // 绘制四个角的装饰线 - 新氧风格科技感设计
            val cornerLength = 40.dp.toPx()
            val cornerOffset = 20.dp.toPx()
            val cornerStroke = Stroke(
                width = 5.dp.toPx(),
                cap = StrokeCap.Round
            )
            
            // 左上角 - 渐变色装饰
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Cyan, Color.Blue),
                    start = Offset(left - cornerOffset, top),
                    end = Offset(left - cornerOffset + cornerLength, top)
                ),
                start = Offset(left - cornerOffset, top),
                end = Offset(left - cornerOffset + cornerLength, top),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Cyan, Color.Blue),
                    start = Offset(left, top - cornerOffset),
                    end = Offset(left, top - cornerOffset + cornerLength)
                ),
                start = Offset(left, top - cornerOffset),
                end = Offset(left, top - cornerOffset + cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            
            // 右上角
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Blue, Color.Magenta),
                    start = Offset(right + cornerOffset, top),
                    end = Offset(right + cornerOffset - cornerLength, top)
                ),
                start = Offset(right + cornerOffset, top),
                end = Offset(right + cornerOffset - cornerLength, top),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Blue, Color.Magenta),
                    start = Offset(right, top - cornerOffset),
                    end = Offset(right, top - cornerOffset + cornerLength)
                ),
                start = Offset(right, top - cornerOffset),
                end = Offset(right, top - cornerOffset + cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            
            // 左下角
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Magenta, Color.Red),
                    start = Offset(left - cornerOffset, bottom),
                    end = Offset(left - cornerOffset + cornerLength, bottom)
                ),
                start = Offset(left - cornerOffset, bottom),
                end = Offset(left - cornerOffset + cornerLength, bottom),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Magenta, Color.Red),
                    start = Offset(left, bottom + cornerOffset),
                    end = Offset(left, bottom + cornerOffset - cornerLength)
                ),
                start = Offset(left, bottom + cornerOffset),
                end = Offset(left, bottom + cornerOffset - cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            
            // 右下角
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Red, Color.Cyan),
                    start = Offset(right + cornerOffset, bottom),
                    end = Offset(right + cornerOffset - cornerLength, bottom)
                ),
                start = Offset(right + cornerOffset, bottom),
                end = Offset(right + cornerOffset - cornerLength, bottom),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
            drawLine(
                brush = Brush.linearGradient(
                    colors = listOf(Color.Red, Color.Cyan),
                    start = Offset(right, bottom + cornerOffset),
                    end = Offset(right, bottom + cornerOffset - cornerLength)
                ),
                start = Offset(right, bottom + cornerOffset),
                end = Offset(right, bottom + cornerOffset - cornerLength),
                strokeWidth = cornerStroke.width,
                cap = cornerStroke.cap
            )
        }
        
        // 顶部简化提示 - 更小更简洁
        Card(
            modifier = Modifier
                .align(Alignment.TopCenter)
                .padding(top = 40.dp, start = 32.dp, end = 32.dp)
                .shadow(4.dp, RoundedCornerShape(16.dp)),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.6f)
            )
        ) {
            Text(
                text = "保持光线充足，面部清晰可见",
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color.White.copy(alpha = 0.9f),
                    fontWeight = FontWeight.Medium
                ),
                textAlign = TextAlign.Center,
                modifier = Modifier.padding(horizontal = 16.dp, vertical = 10.dp)
            )
        }
        
        // 底部状态指示
        Card(
            modifier = Modifier
                .align(Alignment.BottomCenter)
                .padding(bottom = 120.dp, start = 24.dp, end = 24.dp)
                .shadow(6.dp, RoundedCornerShape(16.dp)),
            shape = RoundedCornerShape(16.dp),
            colors = CardDefaults.cardColors(
                containerColor = Color.Black.copy(alpha = 0.6f)
            )
        ) {
            Row(
                modifier = Modifier.padding(16.dp),
                verticalAlignment = Alignment.CenterVertically
            ) {
                // 状态指示灯
                Box(
                    modifier = Modifier
                        .size(12.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(Color.Green, Color.Green.copy(alpha = 0.3f))
                            ),
                            shape = CircleShape
                        )
                )
                Spacer(modifier = Modifier.padding(8.dp))
                Text(
                    text = "准备就绪 · 点击拍照按钮开始检测",
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color.White.copy(alpha = 0.9f)
                    )
                )
            }
        }
    }
}
