{"logs": [{"outputFile": "com.wendy.face.app-mergeDebugResources-56:/values-bs/values-bs.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,174,294,415,535,634,732,847,992,1112,1250,1335,1435,1528,1626,1743,1870,1975,2110,2244,2385,2555,2690,2813,2940,3068,3162,3260,3381,3509,3606,3709,3818,3957,4102,4211,4311,4396,4489,4584,4711,4805,4896,5005,5093,5176,5273,5377,5470,5567,5655,5763,5860,5962,6100,6190,6298", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "169,289,410,530,629,727,842,987,1107,1245,1330,1430,1523,1621,1738,1865,1970,2105,2239,2380,2550,2685,2808,2935,3063,3157,3255,3376,3504,3601,3704,3813,3952,4097,4206,4306,4391,4484,4579,4706,4800,4891,5000,5088,5171,5268,5372,5465,5562,5650,5758,5855,5957,6095,6185,6293,6392"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "6385,6504,6624,6745,6865,6964,7062,7177,7322,7442,7580,7665,7765,7858,7956,8073,8200,8305,8440,8574,8715,8885,9020,9143,9270,9398,9492,9590,9711,9839,9936,10039,10148,10287,10432,10541,10641,10726,10819,10914,11041,11135,11226,11335,11423,11506,11603,11707,11800,11897,11985,12093,12190,12292,12430,12520,12628", "endColumns": "118,119,120,119,98,97,114,144,119,137,84,99,92,97,116,126,104,134,133,140,169,134,122,126,127,93,97,120,127,96,102,108,138,144,108,99,84,92,94,126,93,90,108,87,82,96,103,92,96,87,107,96,101,137,89,107,98", "endOffsets": "6499,6619,6740,6860,6959,7057,7172,7317,7437,7575,7660,7760,7853,7951,8068,8195,8300,8435,8569,8710,8880,9015,9138,9265,9393,9487,9585,9706,9834,9931,10034,10143,10282,10427,10536,10636,10721,10814,10909,11036,11130,11221,11330,11418,11501,11598,11702,11795,11892,11980,12088,12185,12287,12425,12515,12623,12722"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,145", "endColumns": "89,91", "endOffsets": "140,232"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "13832,13922", "endColumns": "89,91", "endOffsets": "13917,14009"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,153,255,353,457,561,663,780", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "148,250,348,452,556,658,775,876"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2830,2928,3030,3128,3232,3336,3438,13463", "endColumns": "97,101,97,103,103,101,116,100", "endOffsets": "2923,3025,3123,3227,3331,3433,3550,13559"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,206,294,388,487,573,650,742,834,919,1000,1086,1159,1236,1315,1392,1472,1542", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "201,289,383,482,568,645,737,829,914,995,1081,1154,1231,1310,1387,1467,1537,1655"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3555,3656,5937,6031,6130,6216,6293,12727,12819,12904,12985,13157,13230,13307,13386,13564,13644,13714", "endColumns": "100,87,93,98,85,76,91,91,84,80,85,72,76,78,76,79,69,117", "endOffsets": "3651,3739,6026,6125,6211,6288,6380,12814,12899,12980,13066,13225,13302,13381,13458,13639,13709,13827"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b463a313bba75631a42e21f95a59b107\\transformed\\play-services-base-18.1.0\\res\\values-bs\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "193,295,451,573,679,825,947,1055,1153,1303,1406,1563,1686,1832,1974,2038,2096", "endColumns": "101,155,121,105,145,121,107,97,149,102,156,122,145,141,63,57,80", "endOffsets": "294,450,572,678,824,946,1054,1152,1302,1405,1562,1685,1831,1973,2037,2095,2176"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3744,3850,4010,4136,4246,4396,4522,4634,4877,5031,5138,5299,5426,5576,5722,5790,5852", "endColumns": "105,159,125,109,149,125,111,101,153,106,160,126,149,145,67,61,84", "endOffsets": "3845,4005,4131,4241,4391,4517,4629,4731,5026,5133,5294,5421,5571,5717,5785,5847,5932"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd665940b510ccfb87ca6845c9679e75\\transformed\\play-services-basement-18.1.0\\res\\values-bs\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "195", "endColumns": "136", "endOffsets": "331"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4736", "endColumns": "140", "endOffsets": "4872"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-bs\\values-bs.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,910,1001,1093,1188,1282,1383,1476,1571,1666,1757,1848,1935,2038,2142,2243,2348,2462,2565,2734,2830", "endColumns": "120,96,106,85,103,121,84,82,90,91,94,93,100,92,94,94,90,90,86,102,103,100,104,113,102,168,95,85", "endOffsets": "221,318,425,511,615,737,822,905,996,1088,1183,1277,1378,1471,1566,1661,1752,1843,1930,2033,2137,2238,2343,2457,2560,2729,2825,2911"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,226,323,430,516,620,742,827,910,1001,1093,1188,1282,1383,1476,1571,1666,1757,1848,1935,2038,2142,2243,2348,2462,2565,2734,13071", "endColumns": "120,96,106,85,103,121,84,82,90,91,94,93,100,92,94,94,90,90,86,102,103,100,104,113,102,168,95,85", "endOffsets": "221,318,425,511,615,737,822,905,996,1088,1183,1277,1378,1471,1566,1661,1752,1843,1930,2033,2137,2238,2343,2457,2560,2729,2825,13152"}}]}]}