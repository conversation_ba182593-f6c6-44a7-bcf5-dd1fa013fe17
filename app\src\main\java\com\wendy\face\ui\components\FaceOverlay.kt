package com.wendy.face.ui.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.detection.FaceRenderer
import com.wendy.face.utils.ImageUtils

/**
 * 人脸覆盖层组件
 * 在图片或相机预览上绘制人脸检测结果
 */
@Composable
fun FaceOverlay(faceMeshes: List<FaceMesh>, imageWidth: Int, imageHeight: Int, isBackCamera: Boolean) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 主要的人脸检测绘制层
        Canvas(modifier = Modifier.fillMaxSize()) {
            if (imageWidth == 0 || imageHeight == 0 || faceMeshes.isEmpty()) return@Canvas

            val viewWidth = size.width
            val viewHeight = size.height

            // 计算图片在视图中的实际显示区域（考虑ContentScale.Fit）
            val displayBounds = ImageUtils.calculateDisplayBounds(
                imageWidth, imageHeight, viewWidth, viewHeight
            )
            val displayWidth = displayBounds[0]
            val displayHeight = displayBounds[1]
            val offsetX = displayBounds[2]
            val offsetY = displayBounds[3]

            val scaleX = displayWidth / imageWidth.toFloat()
            val scaleY = displayHeight / imageHeight.toFloat()

            Log.d("FaceOverlay", "View: ${viewWidth}x${viewHeight}, Image: ${imageWidth}x${imageHeight}")
            Log.d("FaceOverlay", "Display: ${displayWidth}x${displayHeight}, Offset: (${offsetX}, ${offsetY})")
            Log.d("FaceOverlay", "Scale: (${scaleX}, ${scaleY})")

            // 使用FaceRenderer绘制人脸网格
            FaceRenderer.drawFaceMeshes(
                drawScope = this,
                faceMeshes = faceMeshes,
                offsetX = offsetX,
                offsetY = offsetY,
                scaleX = scaleX,
                scaleY = scaleY,
                isBackCamera = isBackCamera,
                imageWidth = imageWidth
            )
        }

        // 右下角半透明检测信息面板
        if (faceMeshes.isNotEmpty()) {
            FaceDetectionInfoPanel(
                faceMeshes = faceMeshes,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(16.dp)
            )
        }
    }
}

/**
 * 右下角半透明检测信息面板
 */
@Composable
private fun FaceDetectionInfoPanel(
    faceMeshes: List<FaceMesh>,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .clip(RoundedCornerShape(12.dp))
            .background(
                brush = Brush.verticalGradient(
                    colors = listOf(
                        Color.Black.copy(alpha = 0.7f),
                        Color.Black.copy(alpha = 0.5f)
                    )
                ),
                shape = RoundedCornerShape(12.dp)
            ),
        colors = CardDefaults.cardColors(
            containerColor = Color.Transparent
        ),
        elevation = CardDefaults.cardElevation(defaultElevation = 8.dp)
    ) {
        Column(
            modifier = Modifier.padding(12.dp),
            horizontalAlignment = Alignment.End
        ) {
            // 检测状态指示
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(6.dp)
            ) {
                // 科技感状态指示灯
                Box(
                    modifier = Modifier
                        .size(8.dp)
                        .background(
                            brush = Brush.radialGradient(
                                colors = listOf(
                                    Color.Cyan,
                                    Color.Cyan.copy(alpha = 0.3f)
                                )
                            ),
                            shape = RoundedCornerShape(50)
                        )
                )
                Text(
                    text = "检测中",
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color.White.copy(alpha = 0.9f),
                        fontSize = 11.sp,
                        fontWeight = FontWeight.Medium
                    )
                )
            }

            Spacer(modifier = Modifier.height(4.dp))

            // 人脸数量信息
            Text(
                text = "${faceMeshes.size} 张人脸",
                style = MaterialTheme.typography.bodySmall.copy(
                    color = Color.Cyan.copy(alpha = 0.8f),
                    fontSize = 10.sp,
                    fontWeight = FontWeight.Bold
                )
            )

            // 关键点数量信息
            if (faceMeshes.isNotEmpty()) {
                val totalPoints = faceMeshes.sumOf { it.allPoints.size }
                Text(
                    text = "$totalPoints 个关键点",
                    style = MaterialTheme.typography.bodySmall.copy(
                        color = Color.White.copy(alpha = 0.7f),
                        fontSize = 9.sp
                    )
                )
            }
        }
    }
}
