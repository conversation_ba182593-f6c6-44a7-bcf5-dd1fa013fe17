package com.wendy.face.ui.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.*
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.detection.FaceRenderer
import com.wendy.face.utils.ImageUtils

/**
 * 人脸覆盖层组件
 * 在图片或相机预览上绘制人脸检测结果
 */
@Composable
fun FaceOverlay(faceMeshes: List<FaceMesh>, imageWidth: Int, imageHeight: Int, isBackCamera: Boolean) {
    Box(modifier = Modifier.fillMaxSize()) {
        // 主要的人脸检测绘制层
        Canvas(modifier = Modifier.fillMaxSize()) {
            if (imageWidth == 0 || imageHeight == 0 || faceMeshes.isEmpty()) return@Canvas

            val viewWidth = size.width
            val viewHeight = size.height

            // 计算图片在视图中的实际显示区域（考虑ContentScale.Fit）
            val displayBounds = ImageUtils.calculateDisplayBounds(
                imageWidth, imageHeight, viewWidth, viewHeight
            )
            val displayWidth = displayBounds[0]
            val displayHeight = displayBounds[1]
            val offsetX = displayBounds[2]
            val offsetY = displayBounds[3]

            val scaleX = displayWidth / imageWidth.toFloat()
            val scaleY = displayHeight / imageHeight.toFloat()

            Log.d("FaceOverlay", "View: ${viewWidth}x${viewHeight}, Image: ${imageWidth}x${imageHeight}")
            Log.d("FaceOverlay", "Display: ${displayWidth}x${displayHeight}, Offset: (${offsetX}, ${offsetY})")
            Log.d("FaceOverlay", "Scale: (${scaleX}, ${scaleY})")

            // 使用FaceRenderer绘制人脸网格
            FaceRenderer.drawFaceMeshes(
                drawScope = this,
                faceMeshes = faceMeshes,
                offsetX = offsetX,
                offsetY = offsetY,
                scaleX = scaleX,
                scaleY = scaleY,
                isBackCamera = isBackCamera,
                imageWidth = imageWidth
            )
        }

        // 右下角半透明检测信息面板
        if (faceMeshes.isNotEmpty()) {
            FaceDetectionInfoPanel(
                faceMeshes = faceMeshes,
                modifier = Modifier
                    .align(Alignment.BottomEnd)
                    .padding(16.dp)
            )
        }
    }
}

/**
 * 右下角半透明检测信息面板
 */
@Composable
private fun FaceDetectionInfoPanel(
    faceMeshes: List<FaceMesh>,
    modifier: Modifier = Modifier
) {
    Box(
        modifier = modifier
            .background(
                color = Color.Black.copy(alpha = 0.6f),
                shape = RoundedCornerShape(8.dp)
            )
            .padding(8.dp)
    ) {
        Column(
            horizontalAlignment = Alignment.End
        ) {
            // 简化的检测状态
            Row(
                verticalAlignment = Alignment.CenterVertically,
                horizontalArrangement = Arrangement.spacedBy(4.dp)
            ) {
                Box(
                    modifier = Modifier
                        .size(6.dp)
                        .background(
                            color = Color.Cyan,
                            shape = RoundedCornerShape(50)
                        )
                )
                Text(
                    text = "检测中",
                    color = Color.White.copy(alpha = 0.9f),
                    fontSize = 10.sp
                )
            }

            Spacer(modifier = Modifier.height(2.dp))

            // 人脸数量
            Text(
                text = "${faceMeshes.size} 张人脸",
                color = Color.Cyan.copy(alpha = 0.8f),
                fontSize = 9.sp
            )
        }
    }
}
