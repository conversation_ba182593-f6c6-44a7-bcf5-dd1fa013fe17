package com.wendy.face.ui.components

import android.util.Log
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import com.google.mlkit.vision.facemesh.FaceMesh
import com.wendy.face.detection.FaceRenderer
import com.wendy.face.utils.ImageUtils

/**
 * 人脸覆盖层组件
 * 在图片或相机预览上绘制人脸检测结果
 */
@Composable
fun FaceOverlay(faceMeshes: List<FaceMesh>, imageWidth: Int, imageHeight: Int, isBackCamera: Boolean) {
    Canvas(modifier = Modifier.fillMaxSize()) {
        if (imageWidth == 0 || imageHeight == 0 || faceMeshes.isEmpty()) return@Canvas

        val viewWidth = size.width
        val viewHeight = size.height

        // 计算图片在视图中的实际显示区域（考虑ContentScale.Fit）
        val displayBounds = ImageUtils.calculateDisplayBounds(
            imageWidth, imageHeight, viewWidth, viewHeight
        )
        val displayWidth = displayBounds[0]
        val displayHeight = displayBounds[1]
        val offsetX = displayBounds[2]
        val offsetY = displayBounds[3]

        val scaleX = displayWidth / imageWidth.toFloat()
        val scaleY = displayHeight / imageHeight.toFloat()

        Log.d("FaceOverlay", "View: ${viewWidth}x${viewHeight}, Image: ${imageWidth}x${imageHeight}")
        Log.d("FaceOverlay", "Display: ${displayWidth}x${displayHeight}, Offset: (${offsetX}, ${offsetY})")
        Log.d("FaceOverlay", "Scale: (${scaleX}, ${scaleY})")

        // 使用FaceRenderer绘制人脸网格
        FaceRenderer.drawFaceMeshes(
            drawScope = this,
            faceMeshes = faceMeshes,
            offsetX = offsetX,
            offsetY = offsetY,
            scaleX = scaleX,
            scaleY = scaleY,
            isBackCamera = isBackCamera,
            imageWidth = imageWidth
        )
    }
}
