<?xml version="1.0" encoding="utf-8"?>
<merger version="3"><dataSet config="com.google.mlkit:face-mesh-detection:16.0.0-beta1" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets"><file name="mlkit_facemesh/data/geometry_pipeline_metadata_landmarks.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\data\geometry_pipeline_metadata_landmarks.binarypb"/><file name="mlkit_facemesh/facedetector-front.f16.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\facedetector-front.f16.tflite"/><file name="mlkit_facemesh/face_landmark_with_attention.tflite" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\face_landmark_with_attention.tflite"/><file name="mlkit_facemesh/face_mesh_graph.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\face_mesh_graph.binarypb"/><file name="mlkit_facemesh/face_short_range_graph.binarypb" path="C:\Users\<USER>\.gradle\caches\8.11.1\transforms\476b0b08ad0be6fbe1ccabec4c32b172\transformed\face-mesh-detection-16.0.0-beta1\assets\mlkit_facemesh\face_short_range_graph.binarypb"/></source></dataSet><dataSet config="main" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\workspace\gitee.com\wendy\face\app\src\main\assets"/></dataSet><dataSet config="debug" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\workspace\gitee.com\wendy\face\app\src\debug\assets"/></dataSet><dataSet config="generated" ignore_pattern="!.svn:!.git:!.ds_store:!*.scc:.*:&lt;dir>_*:!CVS:!thumbs.db:!picasa.ini:!*~"><source path="D:\workspace\gitee.com\wendy\face\app\build\intermediates\shader_assets\debug\compileDebugShaders\out"/></dataSet></merger>