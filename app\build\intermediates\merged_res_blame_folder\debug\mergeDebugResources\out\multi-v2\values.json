{"logs": [{"outputFile": "C:\\Users\\<USER>\\.gradle\\daemon\\8.11.1\\com.wendy.face.app-mergeDebugResources-56:\\values\\values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,111", "endColumns": "55,54", "endOffsets": "106,161"}, "to": {"startLines": "438,439", "startColumns": "4,4", "startOffsets": "29752,29808", "endColumns": "55,54", "endOffsets": "29803,29858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d07ad02806221aff681e526df751834b\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,97", "endColumns": "41,59", "endOffsets": "92,152"}, "to": {"startLines": "272,292", "startColumns": "4,4", "startOffsets": "17634,18656", "endColumns": "41,59", "endOffsets": "17671,18711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fcdaf28699cc4114e6d32da17e246a20\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "65", "endOffsets": "116"}, "to": {"startLines": "262", "startColumns": "4", "startOffsets": "17141", "endColumns": "65", "endOffsets": "17202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d5ee2f8e7d76ed41d62768c46df44c9a\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "2,6,14", "startColumns": "4,4,4", "startOffsets": "55,207,514", "endLines": "5,13,17", "endColumns": "11,11,24", "endOffsets": "202,509,652"}, "to": {"startLines": "4,10,2977", "startColumns": "4,4,4", "startOffsets": "250,511,166146", "endLines": "7,17,2980", "endColumns": "11,11,24", "endOffsets": "397,813,166284"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f9b2f3ac4e57c2a361a436e16b1cb4e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "53", "endOffsets": "104"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "18716", "endColumns": "53", "endOffsets": "18765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd665940b510ccfb87ca6845c9679e75\\transformed\\play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,7", "startColumns": "0,0", "startOffsets": "243,406", "endColumns": "63,166", "endOffsets": "306,572"}, "to": {"startLines": "300,349", "startColumns": "4,4", "startOffsets": "19116,22990", "endColumns": "67,166", "endOffsets": "19179,23152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\229fb45d41e6a7dc95d863a72eae7b4c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,109", "endColumns": "53,66", "endOffsets": "104,171"}, "to": {"startLines": "266,269", "startColumns": "4,4", "startOffsets": "17347,17471", "endColumns": "53,66", "endOffsets": "17396,17533"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "3", "startColumns": "4", "startOffsets": "56", "endColumns": "81", "endOffsets": "133"}, "to": {"startLines": "1852", "startColumns": "4", "startOffsets": "123568", "endColumns": "80", "endOffsets": "123644"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\56cb084021e871cf84ecea51eab2d876\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "42", "endOffsets": "93"}, "to": {"startLines": "291", "startColumns": "4", "startOffsets": "18613", "endColumns": "42", "endOffsets": "18651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9fce76da01519a7e775994f3de797a74\\transformed\\appcompat-resources-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,29,36,47,74", "startColumns": "4,4,4,4,4", "startOffsets": "55,1702,2087,2684,4317", "endLines": "28,35,46,73,78", "endColumns": "24,24,24,24,24", "endOffsets": "1697,2082,2679,4312,4582"}, "to": {"startLines": "2076,2092,2098,3053,3069", "startColumns": "4,4,4,4,4", "startOffsets": "136487,136912,137090,168316,168727", "endLines": "2091,2097,2107,3068,3072", "endColumns": "24,24,24,24,24", "endOffsets": "136907,137085,137369,168722,168849"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,210,218,219,223,227,231,236,242,249,253,257,262,266,270,274,278,282,286,292,296,302,306,312,316,321,325,328,332,338,342,348,352,358,361,365,369,373,377,381,382,383,384,387,390,393,396,400,401,402,403,404,407,409,411,413,418,419,423,429,433,434,436,447,448,452,458,462,463,464,468,495,499,500,504,532,703,729,901,927,958,966,972,986,1008,1013,1018,1028,1037,1046,1050,1057,1065,1072,1073,1082,1085,1088,1092,1096,1100,1103,1104,1109,1114,1124,1129,1136,1142,1143,1146,1150,1155,1157,1159,1162,1165,1167,1171,1174,1181,1184,1187,1191,1193,1197,1199,1201,1203,1207,1215,1223,1235,1241,1250,1253,1264,1267,1268,1273,1274,1279,1348,1418,1419,1429,1438,1439,1441,1445,1448,1451,1454,1457,1460,1463,1466,1470,1473,1476,1479,1483,1486,1490,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1514,1516,1518,1519,1520,1521,1522,1523,1524,1525,1527,1528,1530,1531,1533,1535,1536,1538,1539,1540,1541,1542,1543,1545,1546,1547,1548,1549,1550,1552,1554,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1568,1570,1571,1572,1573,1574,1575,1577,1581,1585,1586,1587,1588,1589,1590,1594,1595,1596,1597,1599,1601,1603,1605,1607,1608,1609,1610,1612,1614,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1627,1630,1631,1632,1633,1635,1637,1638,1640,1641,1643,1645,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1658,1660,1661,1662,1663,1665,1666,1667,1668,1669,1671,1673,1675,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1692,1775,1778,1781,1784,1798,1809,1819,1849,1876,1885,1960,2357,2362,2390,2408,2444,2450,2456,2479,2620,2640,2646,2650,2656,2693,2705,2771,2795,2864,2883,2909", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,160,205,254,295,350,409,471,552,613,688,764,841,919,1004,1086,1162,1238,1315,1393,1499,1605,1684,1764,1821,1879,1953,2028,2093,2159,2219,2280,2352,2425,2492,2560,2619,2678,2737,2796,2855,2909,2963,3016,3070,3124,3178,3232,3306,3385,3458,3532,3603,3675,3747,3820,3877,3935,4008,4082,4156,4231,4303,4376,4446,4517,4577,4638,4707,4776,4846,4920,4996,5060,5137,5213,5290,5355,5424,5501,5576,5645,5713,5790,5856,5917,6014,6079,6148,6247,6318,6377,6435,6492,6551,6615,6686,6758,6830,6902,6974,7041,7109,7177,7236,7299,7363,7453,7544,7604,7670,7737,7803,7873,7937,7990,8057,8118,8185,8298,8356,8419,8484,8549,8624,8697,8769,8818,8879,8940,9001,9063,9127,9191,9255,9320,9383,9443,9504,9570,9629,9689,9751,9822,9882,9950,10036,10123,10213,10300,10388,10470,10553,10643,10734,10786,10844,10889,10955,11019,11076,11133,11187,11244,11292,11341,11392,11426,11473,11522,11568,11600,11664,11726,11786,11843,11917,11987,12065,12119,12189,12274,12322,12368,12429,12492,12558,12622,12693,12756,12821,12885,12946,13007,13059,13132,13206,13275,13350,13424,13498,13639,13709,13762,13840,13930,14018,14114,14204,14786,14875,15122,15403,15655,15940,16333,16810,17032,17254,17530,17757,17987,18217,18447,18677,18904,19323,19549,19974,20204,20632,20851,21134,21342,21473,21700,22126,22351,22778,22999,23424,23544,23820,24121,24445,24736,25050,25187,25318,25423,25665,25832,26036,26244,26515,26627,26739,26844,26961,27175,27321,27461,27547,27895,27983,28229,28647,28896,28978,29076,29693,29793,30045,30469,30724,30818,30907,31144,33196,33438,33540,33793,35977,47101,48617,59840,61368,63125,63751,64171,65232,66497,66753,66989,67536,68030,68635,68833,69413,69977,70352,70470,71008,71165,71361,71634,71890,72060,72201,72265,72630,72997,73673,73937,74275,74628,74722,74908,75214,75476,75601,75728,75967,76178,76297,76490,76667,77122,77303,77425,77684,77797,77984,78086,78193,78322,78597,79105,79601,80478,80772,81342,81491,82223,82395,82479,82815,82907,83185,88594,94146,94208,94838,95452,95543,95656,95885,96045,96197,96368,96534,96703,96870,97033,97276,97446,97619,97790,98064,98263,98468,98798,98882,98978,99074,99172,99272,99374,99476,99578,99680,99782,99882,99978,100090,100219,100342,100473,100604,100702,100816,100910,101050,101184,101280,101392,101492,101608,101704,101816,101916,102056,102192,102356,102486,102644,102794,102935,103079,103214,103326,103476,103604,103732,103868,104000,104130,104260,104372,104512,104658,104802,104940,105006,105096,105172,105276,105366,105468,105576,105684,105784,105864,105956,106054,106164,106242,106348,106440,106544,106654,106776,106939,107096,107176,107276,107366,107476,107566,107807,107901,108007,108099,108199,108311,108425,108541,108657,108751,108865,108977,109079,109199,109321,109403,109507,109627,109753,109851,109945,110033,110145,110261,110383,110495,110670,110786,110872,110964,111076,111200,111267,111393,111461,111589,111733,111861,111930,112025,112140,112253,112352,112461,112572,112683,112784,112889,112989,113119,113210,113333,113427,113539,113625,113729,113825,113913,114031,114135,114239,114365,114453,114561,114661,114751,114861,114945,115047,115131,115185,115249,115355,115441,115551,115635,115755,120899,121017,121132,121264,121979,122671,123188,124787,126320,126708,131443,151705,151965,153475,154508,156521,156783,157139,157969,164751,165885,166179,166402,166729,168779,169427,173278,174480,178559,179774,181183", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,181,182,183,184,185,186,187,188,189,190,191,192,193,194,195,196,197,198,199,200,201,202,203,204,205,206,207,208,209,217,218,222,226,230,235,241,248,252,256,261,265,269,273,277,281,285,291,295,301,305,311,315,320,324,327,331,337,341,347,351,357,360,364,368,372,376,380,381,382,383,386,389,392,395,399,400,401,402,403,406,408,410,412,417,418,422,428,432,433,435,446,447,451,457,461,462,463,467,494,498,499,503,531,702,728,900,926,957,965,971,985,1007,1012,1017,1027,1036,1045,1049,1056,1064,1071,1072,1081,1084,1087,1091,1095,1099,1102,1103,1108,1113,1123,1128,1135,1141,1142,1145,1149,1154,1156,1158,1161,1164,1166,1170,1173,1180,1183,1186,1190,1192,1196,1198,1200,1202,1206,1214,1222,1234,1240,1249,1252,1263,1266,1267,1272,1273,1278,1347,1417,1418,1428,1437,1438,1440,1444,1447,1450,1453,1456,1459,1462,1465,1469,1472,1475,1478,1482,1485,1489,1493,1494,1495,1496,1497,1498,1499,1500,1501,1502,1503,1504,1505,1506,1507,1508,1509,1510,1511,1512,1513,1515,1517,1518,1519,1520,1521,1522,1523,1524,1526,1527,1529,1530,1532,1534,1535,1537,1538,1539,1540,1541,1542,1544,1545,1546,1547,1548,1549,1551,1553,1555,1556,1557,1558,1559,1560,1561,1562,1563,1564,1565,1566,1567,1569,1570,1571,1572,1573,1574,1576,1580,1584,1585,1586,1587,1588,1589,1593,1594,1595,1596,1598,1600,1602,1604,1606,1607,1608,1609,1611,1613,1615,1616,1617,1618,1619,1620,1621,1622,1623,1624,1625,1626,1629,1630,1631,1632,1634,1636,1637,1639,1640,1642,1644,1646,1647,1648,1649,1650,1651,1652,1653,1654,1655,1656,1657,1659,1660,1661,1662,1664,1665,1666,1667,1668,1670,1672,1674,1676,1677,1678,1679,1680,1681,1682,1683,1684,1685,1686,1687,1688,1689,1690,1691,1774,1777,1780,1783,1797,1808,1818,1848,1875,1884,1959,2356,2361,2389,2407,2443,2449,2455,2478,2619,2639,2645,2649,2655,2692,2704,2770,2794,2863,2882,2908,2917", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "155,200,249,290,345,404,466,547,608,683,759,836,914,999,1081,1157,1233,1310,1388,1494,1600,1679,1759,1816,1874,1948,2023,2088,2154,2214,2275,2347,2420,2487,2555,2614,2673,2732,2791,2850,2904,2958,3011,3065,3119,3173,3227,3301,3380,3453,3527,3598,3670,3742,3815,3872,3930,4003,4077,4151,4226,4298,4371,4441,4512,4572,4633,4702,4771,4841,4915,4991,5055,5132,5208,5285,5350,5419,5496,5571,5640,5708,5785,5851,5912,6009,6074,6143,6242,6313,6372,6430,6487,6546,6610,6681,6753,6825,6897,6969,7036,7104,7172,7231,7294,7358,7448,7539,7599,7665,7732,7798,7868,7932,7985,8052,8113,8180,8293,8351,8414,8479,8544,8619,8692,8764,8813,8874,8935,8996,9058,9122,9186,9250,9315,9378,9438,9499,9565,9624,9684,9746,9817,9877,9945,10031,10118,10208,10295,10383,10465,10548,10638,10729,10781,10839,10884,10950,11014,11071,11128,11182,11239,11287,11336,11387,11421,11468,11517,11563,11595,11659,11721,11781,11838,11912,11982,12060,12114,12184,12269,12317,12363,12424,12487,12553,12617,12688,12751,12816,12880,12941,13002,13054,13127,13201,13270,13345,13419,13493,13634,13704,13757,13835,13925,14013,14109,14199,14781,14870,15117,15398,15650,15935,16328,16805,17027,17249,17525,17752,17982,18212,18442,18672,18899,19318,19544,19969,20199,20627,20846,21129,21337,21468,21695,22121,22346,22773,22994,23419,23539,23815,24116,24440,24731,25045,25182,25313,25418,25660,25827,26031,26239,26510,26622,26734,26839,26956,27170,27316,27456,27542,27890,27978,28224,28642,28891,28973,29071,29688,29788,30040,30464,30719,30813,30902,31139,33191,33433,33535,33788,35972,47096,48612,59835,61363,63120,63746,64166,65227,66492,66748,66984,67531,68025,68630,68828,69408,69972,70347,70465,71003,71160,71356,71629,71885,72055,72196,72260,72625,72992,73668,73932,74270,74623,74717,74903,75209,75471,75596,75723,75962,76173,76292,76485,76662,77117,77298,77420,77679,77792,77979,78081,78188,78317,78592,79100,79596,80473,80767,81337,81486,82218,82390,82474,82810,82902,83180,88589,94141,94203,94833,95447,95538,95651,95880,96040,96192,96363,96529,96698,96865,97028,97271,97441,97614,97785,98059,98258,98463,98793,98877,98973,99069,99167,99267,99369,99471,99573,99675,99777,99877,99973,100085,100214,100337,100468,100599,100697,100811,100905,101045,101179,101275,101387,101487,101603,101699,101811,101911,102051,102187,102351,102481,102639,102789,102930,103074,103209,103321,103471,103599,103727,103863,103995,104125,104255,104367,104507,104653,104797,104935,105001,105091,105167,105271,105361,105463,105571,105679,105779,105859,105951,106049,106159,106237,106343,106435,106539,106649,106771,106934,107091,107171,107271,107361,107471,107561,107802,107896,108002,108094,108194,108306,108420,108536,108652,108746,108860,108972,109074,109194,109316,109398,109502,109622,109748,109846,109940,110028,110140,110256,110378,110490,110665,110781,110867,110959,111071,111195,111262,111388,111456,111584,111728,111856,111925,112020,112135,112248,112347,112456,112567,112678,112779,112884,112984,113114,113205,113328,113422,113534,113620,113724,113820,113908,114026,114130,114234,114360,114448,114556,114656,114746,114856,114940,115042,115126,115180,115244,115350,115436,115546,115630,115750,120894,121012,121127,121259,121974,122666,123183,124782,126315,126703,131438,151700,151960,153470,154503,156516,156778,157134,157964,164746,165880,166174,166397,166724,168774,169422,173273,174475,178554,179769,181178,181652"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,212,213,214,215,216,217,218,219,255,256,257,258,264,270,271,273,290,296,297,298,299,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,429,440,441,442,443,444,445,453,454,458,462,466,471,477,484,488,492,497,501,505,509,513,517,521,527,531,537,541,547,551,556,560,563,567,573,577,583,587,593,596,600,604,608,612,616,617,618,619,622,625,628,631,635,636,637,638,639,642,644,646,648,653,654,658,664,668,669,671,682,683,687,693,697,698,699,703,730,734,735,739,767,937,963,1134,1160,1191,1199,1205,1219,1241,1246,1251,1261,1270,1279,1283,1290,1298,1305,1306,1315,1318,1321,1325,1329,1333,1336,1337,1342,1347,1357,1362,1369,1375,1376,1379,1383,1388,1390,1392,1395,1398,1400,1404,1407,1414,1417,1420,1424,1426,1430,1432,1434,1436,1440,1448,1456,1468,1474,1483,1486,1497,1500,1501,1506,1507,1535,1604,1674,1675,1685,1694,1695,1697,1701,1704,1707,1710,1713,1716,1719,1722,1726,1729,1732,1735,1739,1742,1746,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1772,1774,1775,1776,1777,1778,1779,1780,1781,1783,1784,1786,1787,1789,1791,1792,1794,1795,1796,1797,1798,1799,1801,1802,1803,1804,1805,1817,1819,1821,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1837,1838,1839,1840,1841,1842,1844,1848,1853,1854,1855,1856,1857,1858,1862,1863,1864,1865,1867,1869,1871,1873,1875,1876,1877,1878,1880,1882,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1895,1898,1899,1900,1901,1903,1905,1906,1908,1909,1911,1913,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1926,1928,1929,1930,1931,1933,1934,1935,1936,1937,1939,1941,1943,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,1962,2037,2040,2043,2046,2060,2066,2108,2137,2164,2173,2235,2594,2614,2642,2753,2777,2783,2802,2823,2947,2967,2973,2981,2987,3041,3073,3139,3159,3214,3226,3252", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,2004,2081,2159,2265,2371,2450,2530,2587,3447,3521,3596,3661,3727,3787,3848,3920,3993,4060,4128,4187,4246,4305,4364,4423,4477,4531,4584,4638,4692,4746,4932,5006,5085,5158,5232,5303,5375,5447,5661,5718,5776,5849,5923,5997,6072,6144,6217,6287,6448,6508,6611,6680,6749,6819,6893,6969,7033,7110,7186,7263,7328,7397,7474,7549,7618,7686,7763,7829,7890,7987,8052,8121,8220,8291,8350,8408,8465,8524,8588,8659,8731,8803,8875,8947,9014,9082,9150,9209,9272,9336,9426,9517,9577,9643,9710,9776,9846,9910,9963,10030,10091,10158,10271,10329,10392,10457,10522,10597,10670,10742,10791,10852,10913,10974,11036,11100,11164,11228,11293,11356,11416,11477,11543,11602,11662,11724,11795,11855,12411,12497,12584,12674,12761,12849,12931,13014,13104,14173,14225,14283,14328,14394,14458,14515,14572,16749,16806,16854,16903,17258,17538,17585,17676,18581,18873,18937,18999,19059,19327,19401,19471,19549,19603,19673,19758,19806,19852,19913,19976,20042,20106,20177,20240,20305,20369,20430,20491,20543,20616,20690,20759,20834,20908,20982,21123,29281,29863,29941,30031,30119,30215,30305,30887,30976,31223,31504,31756,32041,32434,32911,33133,33355,33631,33858,34088,34318,34548,34778,35005,35424,35650,36075,36305,36733,36952,37235,37443,37574,37801,38227,38452,38879,39100,39525,39645,39921,40222,40546,40837,41151,41288,41419,41524,41766,41933,42137,42345,42616,42728,42840,42945,43062,43276,43422,43562,43648,43996,44084,44330,44748,44997,45079,45177,45769,45869,46121,46545,46800,46894,46983,47220,49244,49486,49588,49841,51997,62529,64045,74676,76204,77961,78587,79007,80068,81333,81589,81825,82372,82866,83471,83669,84249,84813,85188,85306,85844,86001,86197,86470,86726,86896,87037,87101,87466,87833,88509,88773,89111,89464,89558,89744,90050,90312,90437,90564,90803,91014,91133,91326,91503,91958,92139,92261,92520,92633,92820,92922,93029,93158,93433,93941,94437,95314,95608,96178,96327,97059,97231,97315,97651,97743,99307,104553,109942,110004,110582,111166,111257,111370,111599,111759,111911,112082,112248,112417,112584,112747,112990,113160,113333,113504,113778,113977,114182,114512,114596,114692,114788,114886,114986,115088,115190,115292,115394,115496,115596,115692,115804,115933,116056,116187,116318,116416,116530,116624,116764,116898,116994,117106,117206,117322,117418,117530,117630,117770,117906,118070,118200,118358,118508,118649,118793,118928,119040,119190,119318,119446,119582,119714,119844,119974,120086,120984,121130,121274,121412,121478,121568,121644,121748,121838,121940,122048,122156,122256,122336,122428,122526,122636,122714,122820,122912,123016,123126,123248,123411,123649,123729,123829,123919,124029,124119,124360,124454,124560,124652,124752,124864,124978,125094,125210,125304,125418,125530,125632,125752,125874,125956,126060,126180,126306,126404,126498,126586,126698,126814,126936,127048,127223,127339,127425,127517,127629,127753,127820,127946,128014,128142,128286,128414,128483,128578,128693,128806,128905,129014,129125,129236,129337,129442,129542,129672,129763,129886,129980,130092,130178,130282,130378,130466,130584,130688,130792,130918,131006,131114,131214,131304,131414,131498,131600,131684,131738,131802,131908,131994,132104,132188,132447,135063,135181,135296,135376,135737,135970,137374,138718,140079,140467,143242,153146,153785,155142,158975,159726,159988,160503,160882,165160,165766,165995,166289,166504,168004,168854,171880,172624,174755,175095,176406", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,212,213,214,215,216,217,218,219,255,256,257,258,264,270,271,273,290,296,297,298,299,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,429,440,441,442,443,444,452,453,457,461,465,470,476,483,487,491,496,500,504,508,512,516,520,526,530,536,540,546,550,555,559,562,566,572,576,582,586,592,595,599,603,607,611,615,616,617,618,621,624,627,630,634,635,636,637,638,641,643,645,647,652,653,657,663,667,668,670,681,682,686,692,696,697,698,702,729,733,734,738,766,936,962,1133,1159,1190,1198,1204,1218,1240,1245,1250,1260,1269,1278,1282,1289,1297,1304,1305,1314,1317,1320,1324,1328,1332,1335,1336,1341,1346,1356,1361,1368,1374,1375,1378,1382,1387,1389,1391,1394,1397,1399,1403,1406,1413,1416,1419,1423,1425,1429,1431,1433,1435,1439,1447,1455,1467,1473,1482,1485,1496,1499,1500,1505,1506,1511,1603,1673,1674,1684,1693,1694,1696,1700,1703,1706,1709,1712,1715,1718,1721,1725,1728,1731,1734,1738,1741,1745,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1771,1773,1774,1775,1776,1777,1778,1779,1780,1782,1783,1785,1786,1788,1790,1791,1793,1794,1795,1796,1797,1798,1800,1801,1802,1803,1804,1805,1818,1820,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1836,1837,1838,1839,1840,1841,1843,1847,1851,1853,1854,1855,1856,1857,1861,1862,1863,1864,1866,1868,1870,1872,1874,1875,1876,1877,1879,1881,1883,1884,1885,1886,1887,1888,1889,1890,1891,1892,1893,1894,1897,1898,1899,1900,1902,1904,1905,1907,1908,1910,1912,1914,1915,1916,1917,1918,1919,1920,1921,1922,1923,1924,1925,1927,1928,1929,1930,1932,1933,1934,1935,1936,1938,1940,1942,1944,1945,1946,1947,1948,1949,1950,1951,1952,1953,1954,1955,1956,1957,1958,1959,2036,2039,2042,2045,2059,2065,2075,2136,2163,2172,2234,2593,2597,2641,2659,2776,2782,2788,2822,2946,2966,2972,2976,2986,3021,3052,3138,3158,3213,3225,3251,3258", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24,24", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2076,2154,2260,2366,2445,2525,2582,2640,3516,3591,3656,3722,3782,3843,3915,3988,4055,4123,4182,4241,4300,4359,4418,4472,4526,4579,4633,4687,4741,4795,5001,5080,5153,5227,5298,5370,5442,5515,5713,5771,5844,5918,5992,6067,6139,6212,6282,6353,6503,6564,6675,6744,6814,6888,6964,7028,7105,7181,7258,7323,7392,7469,7544,7613,7681,7758,7824,7885,7982,8047,8116,8215,8286,8345,8403,8460,8519,8583,8654,8726,8798,8870,8942,9009,9077,9145,9204,9267,9331,9421,9512,9572,9638,9705,9771,9841,9905,9958,10025,10086,10153,10266,10324,10387,10452,10517,10592,10665,10737,10786,10847,10908,10969,11031,11095,11159,11223,11288,11351,11411,11472,11538,11597,11657,11719,11790,11850,11918,12492,12579,12669,12756,12844,12926,13009,13099,13190,14220,14278,14323,14389,14453,14510,14567,14621,16801,16849,16898,16949,17287,17580,17629,17717,18608,18932,18994,19054,19111,19396,19466,19544,19598,19668,19753,19801,19847,19908,19971,20037,20101,20172,20235,20300,20364,20425,20486,20538,20611,20685,20754,20829,20903,20977,21118,21188,29329,29936,30026,30114,30210,30300,30882,30971,31218,31499,31751,32036,32429,32906,33128,33350,33626,33853,34083,34313,34543,34773,35000,35419,35645,36070,36300,36728,36947,37230,37438,37569,37796,38222,38447,38874,39095,39520,39640,39916,40217,40541,40832,41146,41283,41414,41519,41761,41928,42132,42340,42611,42723,42835,42940,43057,43271,43417,43557,43643,43991,44079,44325,44743,44992,45074,45172,45764,45864,46116,46540,46795,46889,46978,47215,49239,49481,49583,49836,51992,62524,64040,74671,76199,77956,78582,79002,80063,81328,81584,81820,82367,82861,83466,83664,84244,84808,85183,85301,85839,85996,86192,86465,86721,86891,87032,87096,87461,87828,88504,88768,89106,89459,89553,89739,90045,90307,90432,90559,90798,91009,91128,91321,91498,91953,92134,92256,92515,92628,92815,92917,93024,93153,93428,93936,94432,95309,95603,96173,96322,97054,97226,97310,97646,97738,98016,104548,109937,109999,110577,111161,111252,111365,111594,111754,111906,112077,112243,112412,112579,112742,112985,113155,113328,113499,113773,113972,114177,114507,114591,114687,114783,114881,114981,115083,115185,115287,115389,115491,115591,115687,115799,115928,116051,116182,116313,116411,116525,116619,116759,116893,116989,117101,117201,117317,117413,117525,117625,117765,117901,118065,118195,118353,118503,118644,118788,118923,119035,119185,119313,119441,119577,119709,119839,119969,120081,120221,121125,121269,121407,121473,121563,121639,121743,121833,121935,122043,122151,122251,122331,122423,122521,122631,122709,122815,122907,123011,123121,123243,123406,123563,123724,123824,123914,124024,124114,124355,124449,124555,124647,124747,124859,124973,125089,125205,125299,125413,125525,125627,125747,125869,125951,126055,126175,126301,126399,126493,126581,126693,126809,126931,127043,127218,127334,127420,127512,127624,127748,127815,127941,128009,128137,128281,128409,128478,128573,128688,128801,128900,129009,129120,129231,129332,129437,129537,129667,129758,129881,129975,130087,130173,130277,130373,130461,130579,130683,130787,130913,131001,131109,131209,131299,131409,131493,131595,131679,131733,131797,131903,131989,132099,132183,132303,135058,135176,135291,135371,135732,135965,136482,138713,140074,140462,143237,153141,153276,155137,155709,159721,159983,160183,160877,165155,165761,165990,166141,166499,167582,168311,171875,172619,174750,175090,176401,176604"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,15,16,17,18,19,20,21,22,23,24,25,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,74", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "100,173,261,347,428,512,581,646,729,835,921,1041,1095,1164,1225,1294,1383,1478,1552,1649,1742,1840,1989,2080,2168,2264,2362,2426,2494,2581,2675,2742,2814,2886,2987,3096,3172,3241,3289,3355,3419,3493,3550,3607,3679,3729,3783,3854,3925,3995,4064,4122,4198,4269,4343,4429,4479,4549,4614,5329", "endLines": "2,3,4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,73,76", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "168,256,342,423,507,576,641,724,830,916,1036,1090,1159,1220,1289,1378,1473,1547,1644,1737,1835,1984,2075,2163,2259,2357,2421,2489,2576,2670,2737,2809,2881,2982,3091,3167,3236,3284,3350,3414,3488,3545,3602,3674,3724,3778,3849,3920,3990,4059,4117,4193,4264,4338,4424,4474,4544,4609,5324,5477"}, "to": {"startLines": "301,364,365,366,367,368,369,370,371,372,373,376,377,378,379,380,381,382,383,384,385,386,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,1515,1525", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19184,24620,24708,24794,24875,24959,25028,25093,25176,25282,25368,25488,25542,25611,25672,25741,25830,25925,25999,26096,26189,26287,26436,26527,26615,26711,26809,26873,26941,27028,27122,27189,27261,27333,27434,27543,27619,27688,27736,27802,27866,27940,27997,28054,28126,28176,28230,28301,28372,28442,28511,28569,28645,28716,28790,28876,28926,28996,98133,98848", "endLines": "301,364,365,366,367,368,369,370,371,372,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,1524,1527", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19252,24703,24789,24870,24954,25023,25088,25171,25277,25363,25483,25537,25606,25667,25736,25825,25920,25994,26091,26184,26282,26431,26522,26610,26706,26804,26868,26936,27023,27117,27184,27256,27328,27429,27538,27614,27683,27731,27797,27861,27935,27992,28049,28121,28171,28225,28296,28367,28437,28506,28564,28640,28711,28785,28871,28921,28991,29056,98843,98996"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "1", "startColumns": "4", "startOffsets": "16", "endColumns": "43", "endOffsets": "55"}, "to": {"startLines": "331", "startColumns": "4", "startOffsets": "21276", "endColumns": "43", "endOffsets": "21315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,98,99,103,104,105,106,112,122,155,176,209", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,115,187,275,340,406,475,538,608,676,748,818,879,953,1026,1087,1148,1210,1274,1336,1397,1465,1565,1625,1691,1764,1833,1890,1942,2004,2076,2152,4127,4162,4197,4252,4315,4370,4428,4486,4547,4610,4667,4718,4768,4829,4886,4952,4986,5021,5056,5126,5193,5265,5334,5403,5477,5549,5637,5708,5825,6026,6136,6337,6466,6538,6605,6808,7109,8840,9521,10203", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,97,98,102,103,104,105,111,121,154,175,208,214", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "110,182,270,335,401,470,533,603,671,743,813,874,948,1021,1082,1143,1205,1269,1331,1392,1460,1560,1620,1686,1759,1828,1885,1937,1999,2071,2147,2212,4157,4192,4247,4310,4365,4423,4481,4542,4605,4662,4713,4763,4824,4881,4947,4981,5016,5051,5121,5188,5260,5329,5398,5472,5544,5632,5703,5820,6021,6131,6332,6461,6533,6600,6803,7104,8835,9516,10198,10365"}, "to": {"startLines": "9,28,29,43,44,75,76,181,182,183,184,185,186,187,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,220,221,222,267,268,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,332,333,334,335,336,337,338,434,1806,1807,1811,1812,1816,1960,1961,2598,2604,2660,2693,2714,2747", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2645,2710,4800,4869,11923,11993,12061,12133,12203,12264,12338,13195,13256,13317,13379,13443,13505,13566,13634,13734,13794,13860,13933,14002,14059,14111,14626,14698,14774,17401,17436,17722,17777,17840,17895,17953,18011,18072,18135,18192,18243,18293,18354,18411,18477,18511,18546,19257,21320,21387,21459,21528,21597,21671,21743,29508,120226,120343,120544,120654,120855,132308,132380,153281,153484,155714,157445,158126,158808", "endLines": "9,28,29,43,44,75,76,181,182,183,184,185,186,187,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,220,221,222,267,268,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,332,333,334,335,336,337,338,434,1806,1810,1811,1815,1816,1960,1961,2603,2613,2692,2713,2746,2752", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66,24,24,24,24,24,24", "endOffsets": "506,1550,1638,2705,2771,4864,4927,11988,12056,12128,12198,12259,12333,12406,13251,13312,13374,13438,13500,13561,13629,13729,13789,13855,13928,13997,14054,14106,14168,14693,14769,14834,17431,17466,17772,17835,17890,17948,18006,18067,18130,18187,18238,18288,18349,18406,18472,18506,18541,18576,19322,21382,21454,21523,21592,21666,21738,21826,29574,120338,120539,120649,120850,120979,132375,132442,153479,153780,157440,158121,158803,158970"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "7,2,3,4,5,6,8", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "286,55,102,149,196,241,328", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "323,97,144,191,236,281,365"}, "to": {"startLines": "34,85,86,87,98,99,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1962,5520,5567,5614,6358,6403,6569", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1999,5562,5609,5656,6398,6443,6606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b463a313bba75631a42e21f95a59b107\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,33,46", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "215,301,377,463,549,625,702,778,951,1052,1233,1354,1457,1637,1756,1868,1967,2155,2256,2437,2558,2733,2877,2936,2994,3164,3475", "endLines": "4,5,6,7,8,9,10,11,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,45,64", "endColumns": "85,75,85,85,75,76,75,75,100,180,120,102,179,118,111,98,187,100,180,120,174,143,58,57,74,20,20", "endOffsets": "300,376,462,548,624,701,777,853,1051,1232,1353,1456,1636,1755,1867,1966,2154,2255,2436,2557,2732,2876,2935,2993,3068,3474,3887"}, "to": {"startLines": "45,46,47,48,49,50,51,52,341,342,343,344,345,346,347,348,350,351,352,353,354,355,356,357,358,2789,3022", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2776,2866,2946,3036,3126,3206,3287,3367,21950,22055,22236,22361,22468,22648,22771,22887,23157,23345,23450,23631,23756,23931,24079,24142,24204,160188,167587", "endLines": "45,46,47,48,49,50,51,52,341,342,343,344,345,346,347,348,350,351,352,353,354,355,356,357,358,2801,3040", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78,20,20", "endOffsets": "2861,2941,3031,3121,3201,3282,3362,3442,22050,22231,22356,22463,22643,22766,22882,22985,23340,23445,23626,23751,23926,24074,24137,24199,24278,160498,167999"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,61,64", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,209,268,328,388,448,508,568,628,688,748,808,868,927,987,1047,1107,1167,1227,1287,1347,1407,1467,1527,1586,1646,1706,1765,1824,1883,1942,2001,2060,2134,2192,2247,2298,2353,2406,2471,2525,2591,2692,2750,2802,2862,2924,2978,3028,3082,3128,3174,3216,3256,3303,3339,3429,3541,3652", "endLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,60,63,67", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "204,263,323,383,443,503,563,623,683,743,803,863,922,982,1042,1102,1162,1222,1282,1342,1402,1462,1522,1581,1641,1701,1760,1819,1878,1937,1996,2055,2129,2187,2242,2293,2348,2401,2466,2520,2586,2687,2745,2797,2857,2919,2973,3023,3077,3123,3169,3211,3251,3298,3334,3424,3536,3647,3842"}, "to": {"startLines": "223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,259,260,261,263,265,295,339,340,359,360,361,362,363,425,426,427,428,430,431,432,433,435,436,437,1512,1528,1531", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14839,14898,14957,15017,15077,15137,15197,15257,15317,15377,15437,15497,15557,15616,15676,15736,15796,15856,15916,15976,16036,16096,16156,16216,16275,16335,16395,16454,16513,16572,16631,16690,16954,17028,17086,17207,17292,18820,21831,21896,24283,24349,24450,24508,24560,29061,29123,29177,29227,29334,29380,29426,29468,29579,29626,29662,98021,99001,99112", "endLines": "223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,259,260,261,263,265,295,339,340,359,360,361,362,363,425,426,427,428,430,431,432,433,435,436,437,1514,1530,1534", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "14893,14952,15012,15072,15132,15192,15252,15312,15372,15432,15492,15552,15611,15671,15731,15791,15851,15911,15971,16031,16091,16151,16211,16270,16330,16390,16449,16508,16567,16626,16685,16744,17023,17081,17136,17253,17342,18868,21891,21945,24344,24445,24503,24555,24615,29118,29172,29222,29276,29375,29421,29463,29503,29621,29657,29747,98128,99107,99302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e1debd6c2199648715ca1c7f082d854\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "49", "endOffsets": "100"}, "to": {"startLines": "294", "startColumns": "4", "startOffsets": "18770", "endColumns": "49", "endOffsets": "18815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d4c5419693f837eebddb887747f95a1b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "2", "startColumns": "4", "startOffsets": "55", "endColumns": "82", "endOffsets": "133"}, "to": {"startLines": "330", "startColumns": "4", "startOffsets": "21193", "endColumns": "82", "endOffsets": "21271"}}]}, {"outputFile": "com.wendy.face.app-mergeDebugResources-56:/values/values.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "438,439", "startColumns": "4,4", "startOffsets": "29752,29808", "endColumns": "55,54", "endOffsets": "29803,29858"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d07ad02806221aff681e526df751834b\\transformed\\activity-1.8.2\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "272,292", "startColumns": "4,4", "startOffsets": "17634,18656", "endColumns": "41,59", "endOffsets": "17671,18711"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\fcdaf28699cc4114e6d32da17e246a20\\transformed\\ui-graphics-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "262", "startColumns": "4", "startOffsets": "17141", "endColumns": "65", "endOffsets": "17202"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d5ee2f8e7d76ed41d62768c46df44c9a\\transformed\\camera-view-1.3.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "4,10", "startColumns": "4,4", "startOffsets": "250,511", "endLines": "7,17", "endColumns": "11,11", "endOffsets": "397,813"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\9f9b2f3ac4e57c2a361a436e16b1cb4e\\transformed\\savedstate-1.2.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "293", "startColumns": "4", "startOffsets": "18716", "endColumns": "53", "endOffsets": "18765"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd665940b510ccfb87ca6845c9679e75\\transformed\\play-services-basement-18.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "300,349", "startColumns": "4,4", "startOffsets": "19116,22990", "endColumns": "67,166", "endOffsets": "19179,23152"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\229fb45d41e6a7dc95d863a72eae7b4c\\transformed\\customview-poolingcontainer-1.0.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1", "startColumns": "-1,-1", "startOffsets": "-1,-1"}, "to": {"startLines": "266,269", "startColumns": "4,4", "startOffsets": "17347,17471", "endColumns": "53,66", "endOffsets": "17396,17533"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\themes.xml", "from": {"startLines": "-1,6", "startColumns": "-1,4", "startOffsets": "-1,168", "endLines": "-1,14", "endColumns": "-1,12", "endOffsets": "-1,728"}, "to": {"startLines": "1852,1853", "startColumns": "4,4", "startOffsets": "123568,123649", "endLines": "1852,1861", "endColumns": "80,12", "endOffsets": "123644,124209"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\56cb084021e871cf84ecea51eab2d876\\transformed\\lifecycle-runtime-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "291", "startColumns": "4", "startOffsets": "18613", "endColumns": "42", "endOffsets": "18651"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,212,213,214,215,216,217,218,219,255,256,257,258,264,270,271,273,290,296,297,298,299,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,429,440,441,442,443,444,445,453,454,458,462,466,471,477,484,488,492,497,501,505,509,513,517,521,527,531,537,541,547,551,556,560,563,567,573,577,583,587,593,596,600,604,608,612,616,617,618,619,622,625,628,631,635,636,637,638,639,642,644,646,648,653,654,658,664,668,669,671,682,683,687,693,697,698,699,703,730,734,735,739,767,937,963,1134,1160,1191,1199,1205,1219,1241,1246,1251,1261,1270,1279,1283,1290,1298,1305,1306,1315,1318,1321,1325,1329,1333,1336,1337,1342,1347,1357,1362,1369,1375,1376,1379,1383,1388,1390,1392,1395,1398,1400,1404,1407,1414,1417,1420,1424,1426,1430,1432,1434,1436,1440,1448,1456,1468,1474,1483,1486,1497,1500,1501,1506,1507,1535,1604,1674,1675,1685,1694,1695,1697,1701,1704,1707,1710,1713,1716,1719,1722,1726,1729,1732,1735,1739,1742,1746,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1770,1772,1774,1775,1776,1777,1778,1779,1780,1781,1783,1784,1786,1787,1789,1791,1792,1794,1795,1796,1797,1798,1799,1801,1802,1803,1804,1805,1817,1819,1821,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1835,1837,1838,1839,1840,1841,1842,1844,1848,1862,1863,1864,1865,1866,1867,1871,1872,1873,1874,1876,1878,1880,1882,1884,1885,1886,1887,1889,1891,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1904,1907,1908,1909,1910,1912,1914,1915,1917,1918,1920,1922,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1935,1937,1938,1939,1940,1942,1943,1944,1945,1946,1948,1950,1952,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "150,205,402,818,859,914,973,1035,1116,1177,1252,1328,1405,1643,1728,1810,1886,2004,2081,2159,2265,2371,2450,2530,2587,3447,3521,3596,3661,3727,3787,3848,3920,3993,4060,4128,4187,4246,4305,4364,4423,4477,4531,4584,4638,4692,4746,4932,5006,5085,5158,5232,5303,5375,5447,5661,5718,5776,5849,5923,5997,6072,6144,6217,6287,6448,6508,6611,6680,6749,6819,6893,6969,7033,7110,7186,7263,7328,7397,7474,7549,7618,7686,7763,7829,7890,7987,8052,8121,8220,8291,8350,8408,8465,8524,8588,8659,8731,8803,8875,8947,9014,9082,9150,9209,9272,9336,9426,9517,9577,9643,9710,9776,9846,9910,9963,10030,10091,10158,10271,10329,10392,10457,10522,10597,10670,10742,10791,10852,10913,10974,11036,11100,11164,11228,11293,11356,11416,11477,11543,11602,11662,11724,11795,11855,12411,12497,12584,12674,12761,12849,12931,13014,13104,14173,14225,14283,14328,14394,14458,14515,14572,16749,16806,16854,16903,17258,17538,17585,17676,18581,18873,18937,18999,19059,19327,19401,19471,19549,19603,19673,19758,19806,19852,19913,19976,20042,20106,20177,20240,20305,20369,20430,20491,20543,20616,20690,20759,20834,20908,20982,21123,29281,29863,29941,30031,30119,30215,30305,30887,30976,31223,31504,31756,32041,32434,32911,33133,33355,33631,33858,34088,34318,34548,34778,35005,35424,35650,36075,36305,36733,36952,37235,37443,37574,37801,38227,38452,38879,39100,39525,39645,39921,40222,40546,40837,41151,41288,41419,41524,41766,41933,42137,42345,42616,42728,42840,42945,43062,43276,43422,43562,43648,43996,44084,44330,44748,44997,45079,45177,45769,45869,46121,46545,46800,46894,46983,47220,49244,49486,49588,49841,51997,62529,64045,74676,76204,77961,78587,79007,80068,81333,81589,81825,82372,82866,83471,83669,84249,84813,85188,85306,85844,86001,86197,86470,86726,86896,87037,87101,87466,87833,88509,88773,89111,89464,89558,89744,90050,90312,90437,90564,90803,91014,91133,91326,91503,91958,92139,92261,92520,92633,92820,92922,93029,93158,93433,93941,94437,95314,95608,96178,96327,97059,97231,97315,97651,97743,99307,104553,109942,110004,110582,111166,111257,111370,111599,111759,111911,112082,112248,112417,112584,112747,112990,113160,113333,113504,113778,113977,114182,114512,114596,114692,114788,114886,114986,115088,115190,115292,115394,115496,115596,115692,115804,115933,116056,116187,116318,116416,116530,116624,116764,116898,116994,117106,117206,117322,117418,117530,117630,117770,117906,118070,118200,118358,118508,118649,118793,118928,119040,119190,119318,119446,119582,119714,119844,119974,120086,120984,121130,121274,121412,121478,121568,121644,121748,121838,121940,122048,122156,122256,122336,122428,122526,122636,122714,122820,122912,123016,123126,123248,123411,124214,124294,124394,124484,124594,124684,124925,125019,125125,125217,125317,125429,125543,125659,125775,125869,125983,126095,126197,126317,126439,126521,126625,126745,126871,126969,127063,127151,127263,127379,127501,127613,127788,127904,127990,128082,128194,128318,128385,128511,128579,128707,128851,128979,129048,129143,129258,129371,129470,129579,129690,129801,129902,130007,130107,130237,130328,130451,130545,130657,130743,130847,130943,131031,131149,131253,131357,131483,131571,131679,131779,131869,131979,132063,132165,132249,132303,132367,132473,132559,132669,132753", "endLines": "2,3,8,18,19,20,21,22,23,24,25,26,27,30,31,32,33,35,36,37,38,39,40,41,42,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,77,78,79,80,81,82,83,84,88,89,90,91,92,93,94,95,96,97,100,101,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117,118,119,120,121,122,123,124,125,126,127,128,129,130,131,132,133,134,135,136,137,138,139,140,141,142,143,144,145,146,147,148,149,150,151,152,153,154,155,156,157,158,159,160,161,162,163,164,165,166,167,168,169,170,171,172,173,174,175,176,177,178,179,180,188,189,190,191,192,193,194,195,196,212,213,214,215,216,217,218,219,255,256,257,258,264,270,271,273,290,296,297,298,299,303,304,305,306,307,308,309,310,311,312,313,314,315,316,317,318,319,320,321,322,323,324,325,326,327,328,329,429,440,441,442,443,444,452,453,457,461,465,470,476,483,487,491,496,500,504,508,512,516,520,526,530,536,540,546,550,555,559,562,566,572,576,582,586,592,595,599,603,607,611,615,616,617,618,621,624,627,630,634,635,636,637,638,641,643,645,647,652,653,657,663,667,668,670,681,682,686,692,696,697,698,702,729,733,734,738,766,936,962,1133,1159,1190,1198,1204,1218,1240,1245,1250,1260,1269,1278,1282,1289,1297,1304,1305,1314,1317,1320,1324,1328,1332,1335,1336,1341,1346,1356,1361,1368,1374,1375,1378,1382,1387,1389,1391,1394,1397,1399,1403,1406,1413,1416,1419,1423,1425,1429,1431,1433,1435,1439,1447,1455,1467,1473,1482,1485,1496,1499,1500,1505,1506,1511,1603,1673,1674,1684,1693,1694,1696,1700,1703,1706,1709,1712,1715,1718,1721,1725,1728,1731,1734,1738,1741,1745,1749,1750,1751,1752,1753,1754,1755,1756,1757,1758,1759,1760,1761,1762,1763,1764,1765,1766,1767,1768,1769,1771,1773,1774,1775,1776,1777,1778,1779,1780,1782,1783,1785,1786,1788,1790,1791,1793,1794,1795,1796,1797,1798,1800,1801,1802,1803,1804,1805,1818,1820,1822,1823,1824,1825,1826,1827,1828,1829,1830,1831,1832,1833,1834,1836,1837,1838,1839,1840,1841,1843,1847,1851,1862,1863,1864,1865,1866,1870,1871,1872,1873,1875,1877,1879,1881,1883,1884,1885,1886,1888,1890,1892,1893,1894,1895,1896,1897,1898,1899,1900,1901,1902,1903,1906,1907,1908,1909,1911,1913,1914,1916,1917,1919,1921,1923,1924,1925,1926,1927,1928,1929,1930,1931,1932,1933,1934,1936,1937,1938,1939,1941,1942,1943,1944,1945,1947,1949,1951,1953,1954,1955,1956,1957,1958,1959,1960,1961,1962,1963,1964,1965,1966,1967,1968", "endColumns": "54,44,48,40,54,58,61,80,60,74,75,76,77,84,81,75,75,76,77,105,105,78,79,56,57,73,74,64,65,59,60,71,72,66,67,58,58,58,58,58,53,53,52,53,53,53,53,73,78,72,73,70,71,71,72,56,57,72,73,73,74,71,72,69,70,59,60,68,68,69,73,75,63,76,75,76,64,68,76,74,68,67,76,65,60,96,64,68,98,70,58,57,56,58,63,70,71,71,71,71,66,67,67,58,62,63,89,90,59,65,66,65,69,63,52,66,60,66,112,57,62,64,64,74,72,71,48,60,60,60,61,63,63,63,64,62,59,60,65,58,59,61,70,59,67,85,86,89,86,87,81,82,89,90,51,57,44,65,63,56,56,53,56,47,48,50,33,46,48,45,31,63,61,59,56,73,69,77,53,69,84,47,45,60,62,65,63,70,62,64,63,60,60,51,72,73,68,74,73,73,140,69,52,77,89,87,95,89,12,88,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,136,130,104,12,12,12,12,12,111,111,104,116,12,12,12,12,12,87,12,12,12,81,12,12,99,12,12,12,93,88,12,12,12,101,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,117,12,12,12,12,12,12,12,63,12,12,12,12,12,12,93,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,12,91,12,12,12,61,12,12,90,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,12,83,95,95,97,99,101,101,101,101,101,99,95,111,128,122,130,130,97,113,93,12,12,95,111,99,115,95,111,99,12,135,12,129,12,12,140,12,134,111,149,127,127,12,131,129,129,111,139,12,12,12,65,89,75,103,89,101,107,107,99,79,91,97,12,77,105,91,103,109,12,12,12,79,99,89,109,89,12,93,105,91,12,12,12,12,12,93,113,111,12,12,12,81,103,119,125,97,93,87,111,115,121,111,12,115,85,91,12,12,66,12,67,12,12,12,68,94,114,112,98,108,110,110,100,104,99,12,90,122,93,12,85,103,95,87,12,12,12,12,87,107,99,89,109,83,101,83,53,63,105,85,109,83,119", "endOffsets": "200,245,446,854,909,968,1030,1111,1172,1247,1323,1400,1478,1723,1805,1881,1957,2076,2154,2260,2366,2445,2525,2582,2640,3516,3591,3656,3722,3782,3843,3915,3988,4055,4123,4182,4241,4300,4359,4418,4472,4526,4579,4633,4687,4741,4795,5001,5080,5153,5227,5298,5370,5442,5515,5713,5771,5844,5918,5992,6067,6139,6212,6282,6353,6503,6564,6675,6744,6814,6888,6964,7028,7105,7181,7258,7323,7392,7469,7544,7613,7681,7758,7824,7885,7982,8047,8116,8215,8286,8345,8403,8460,8519,8583,8654,8726,8798,8870,8942,9009,9077,9145,9204,9267,9331,9421,9512,9572,9638,9705,9771,9841,9905,9958,10025,10086,10153,10266,10324,10387,10452,10517,10592,10665,10737,10786,10847,10908,10969,11031,11095,11159,11223,11288,11351,11411,11472,11538,11597,11657,11719,11790,11850,11918,12492,12579,12669,12756,12844,12926,13009,13099,13190,14220,14278,14323,14389,14453,14510,14567,14621,16801,16849,16898,16949,17287,17580,17629,17717,18608,18932,18994,19054,19111,19396,19466,19544,19598,19668,19753,19801,19847,19908,19971,20037,20101,20172,20235,20300,20364,20425,20486,20538,20611,20685,20754,20829,20903,20977,21118,21188,29329,29936,30026,30114,30210,30300,30882,30971,31218,31499,31751,32036,32429,32906,33128,33350,33626,33853,34083,34313,34543,34773,35000,35419,35645,36070,36300,36728,36947,37230,37438,37569,37796,38222,38447,38874,39095,39520,39640,39916,40217,40541,40832,41146,41283,41414,41519,41761,41928,42132,42340,42611,42723,42835,42940,43057,43271,43417,43557,43643,43991,44079,44325,44743,44992,45074,45172,45764,45864,46116,46540,46795,46889,46978,47215,49239,49481,49583,49836,51992,62524,64040,74671,76199,77956,78582,79002,80063,81328,81584,81820,82367,82861,83466,83664,84244,84808,85183,85301,85839,85996,86192,86465,86721,86891,87032,87096,87461,87828,88504,88768,89106,89459,89553,89739,90045,90307,90432,90559,90798,91009,91128,91321,91498,91953,92134,92256,92515,92628,92815,92917,93024,93153,93428,93936,94432,95309,95603,96173,96322,97054,97226,97310,97646,97738,98016,104548,109937,109999,110577,111161,111252,111365,111594,111754,111906,112077,112243,112412,112579,112742,112985,113155,113328,113499,113773,113972,114177,114507,114591,114687,114783,114881,114981,115083,115185,115287,115389,115491,115591,115687,115799,115928,116051,116182,116313,116411,116525,116619,116759,116893,116989,117101,117201,117317,117413,117525,117625,117765,117901,118065,118195,118353,118503,118644,118788,118923,119035,119185,119313,119441,119577,119709,119839,119969,120081,120221,121125,121269,121407,121473,121563,121639,121743,121833,121935,122043,122151,122251,122331,122423,122521,122631,122709,122815,122907,123011,123121,123243,123406,123563,124289,124389,124479,124589,124679,124920,125014,125120,125212,125312,125424,125538,125654,125770,125864,125978,126090,126192,126312,126434,126516,126620,126740,126866,126964,127058,127146,127258,127374,127496,127608,127783,127899,127985,128077,128189,128313,128380,128506,128574,128702,128846,128974,129043,129138,129253,129366,129465,129574,129685,129796,129897,130002,130102,130232,130323,130446,130540,130652,130738,130842,130938,131026,131144,131248,131352,131478,131566,131674,131774,131864,131974,132058,132160,132244,132298,132362,132468,132554,132664,132748,132868"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "301,364,365,366,367,368,369,370,371,372,373,376,377,378,379,380,381,382,383,384,385,386,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,1515,1525", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "19184,24620,24708,24794,24875,24959,25028,25093,25176,25282,25368,25488,25542,25611,25672,25741,25830,25925,25999,26096,26189,26287,26436,26527,26615,26711,26809,26873,26941,27028,27122,27189,27261,27333,27434,27543,27619,27688,27736,27802,27866,27940,27997,28054,28126,28176,28230,28301,28372,28442,28511,28569,28645,28716,28790,28876,28926,28996,98133,98848", "endLines": "301,364,365,366,367,368,369,370,371,372,375,376,377,378,379,380,381,382,383,384,385,388,389,390,391,392,393,394,395,396,397,398,399,400,401,402,403,404,405,406,407,408,409,410,411,412,413,414,415,416,417,418,419,420,421,422,423,424,1524,1527", "endColumns": "72,87,85,80,83,68,64,82,105,85,13,53,68,60,68,88,94,73,96,92,97,13,90,87,95,97,63,67,86,93,66,71,71,100,108,75,68,47,65,63,73,56,56,71,49,53,70,70,69,68,57,75,70,73,85,49,69,64,12,12", "endOffsets": "19252,24703,24789,24870,24954,25023,25088,25171,25277,25363,25483,25537,25606,25667,25736,25825,25920,25994,26091,26184,26282,26431,26522,26610,26706,26804,26868,26936,27023,27117,27184,27256,27328,27429,27538,27614,27683,27731,27797,27861,27935,27992,28049,28121,28171,28225,28296,28367,28437,28506,28564,28640,28711,28785,28871,28921,28991,29056,98843,98996"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\strings.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "331", "startColumns": "4", "startOffsets": "21276", "endColumns": "43", "endOffsets": "21315"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "9,28,29,43,44,75,76,181,182,183,184,185,186,187,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,220,221,222,267,268,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,332,333,334,335,336,337,338,434,1806,1807,1811,1812,1816,1969,1970", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "451,1483,1555,2645,2710,4800,4869,11923,11993,12061,12133,12203,12264,12338,13195,13256,13317,13379,13443,13505,13566,13634,13734,13794,13860,13933,14002,14059,14111,14626,14698,14774,17401,17436,17722,17777,17840,17895,17953,18011,18072,18135,18192,18243,18293,18354,18411,18477,18511,18546,19257,21320,21387,21459,21528,21597,21671,21743,29508,120226,120343,120544,120654,120855,132873,132945", "endLines": "9,28,29,43,44,75,76,181,182,183,184,185,186,187,197,198,199,200,201,202,203,204,205,206,207,208,209,210,211,220,221,222,267,268,274,275,276,277,278,279,280,281,282,283,284,285,286,287,288,289,302,332,333,334,335,336,337,338,434,1806,1810,1811,1815,1816,1969,1970", "endColumns": "59,71,87,64,65,68,62,69,67,71,69,60,73,72,60,60,61,63,61,60,67,99,59,65,72,68,56,51,61,71,75,64,34,34,54,62,54,57,57,60,62,56,50,49,60,56,65,33,34,34,69,66,71,68,68,73,71,87,70,116,12,109,12,128,71,66", "endOffsets": "506,1550,1638,2705,2771,4864,4927,11988,12056,12128,12198,12259,12333,12406,13251,13312,13374,13438,13500,13561,13629,13729,13789,13855,13928,13997,14054,14106,14168,14693,14769,14834,17431,17466,17772,17835,17890,17948,18006,18067,18130,18187,18238,18288,18349,18406,18472,18506,18541,18576,19322,21382,21454,21523,21592,21666,21738,21826,29574,120338,120539,120649,120850,120979,132940,133007"}}, {"source": "D:\\workspace\\gitee.com\\wendy\\face\\app\\src\\main\\res\\values\\colors.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "34,85,86,87,98,99,102", "startColumns": "4,4,4,4,4,4,4", "startOffsets": "1962,5520,5567,5614,6358,6403,6569", "endColumns": "41,46,46,46,44,44,41", "endOffsets": "1999,5562,5609,5656,6398,6443,6606"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b463a313bba75631a42e21f95a59b107\\transformed\\play-services-base-18.1.0\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "45,46,47,48,49,50,51,52,341,342,343,344,345,346,347,348,350,351,352,353,354,355,356,357,358", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "2776,2866,2946,3036,3126,3206,3287,3367,21950,22055,22236,22361,22468,22648,22771,22887,23157,23345,23450,23631,23756,23931,24079,24142,24204", "endColumns": "89,79,89,89,79,80,79,79,104,180,124,106,179,122,115,102,187,104,180,124,174,147,62,61,78", "endOffsets": "2861,2941,3031,3121,3201,3282,3362,3442,22050,22231,22356,22463,22643,22766,22882,22985,23340,23445,23626,23751,23926,24074,24137,24199,24278"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values\\values.xml", "from": {"startLines": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startColumns": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1", "startOffsets": "-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1,-1"}, "to": {"startLines": "223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,259,260,261,263,265,295,339,340,359,360,361,362,363,425,426,427,428,430,431,432,433,435,436,437,1512,1528,1531", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "14839,14898,14957,15017,15077,15137,15197,15257,15317,15377,15437,15497,15557,15616,15676,15736,15796,15856,15916,15976,16036,16096,16156,16216,16275,16335,16395,16454,16513,16572,16631,16690,16954,17028,17086,17207,17292,18820,21831,21896,24283,24349,24450,24508,24560,29061,29123,29177,29227,29334,29380,29426,29468,29579,29626,29662,98021,99001,99112", "endLines": "223,224,225,226,227,228,229,230,231,232,233,234,235,236,237,238,239,240,241,242,243,244,245,246,247,248,249,250,251,252,253,254,259,260,261,263,265,295,339,340,359,360,361,362,363,425,426,427,428,430,431,432,433,435,436,437,1514,1530,1534", "endColumns": "58,58,59,59,59,59,59,59,59,59,59,59,58,59,59,59,59,59,59,59,59,59,59,58,59,59,58,58,58,58,58,58,73,57,54,50,54,52,64,53,65,100,57,51,59,61,53,49,53,45,45,41,39,46,35,89,12,12,12", "endOffsets": "14893,14952,15012,15072,15132,15192,15252,15312,15372,15432,15492,15552,15611,15671,15731,15791,15851,15911,15971,16031,16091,16151,16211,16270,16330,16390,16449,16508,16567,16626,16685,16744,17023,17081,17136,17253,17342,18868,21891,21945,24344,24445,24503,24555,24615,29118,29172,29222,29276,29375,29421,29463,29503,29621,29657,29747,98128,99107,99302"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\2e1debd6c2199648715ca1c7f082d854\\transformed\\lifecycle-viewmodel-release\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "294", "startColumns": "4", "startOffsets": "18770", "endColumns": "49", "endOffsets": "18815"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\d4c5419693f837eebddb887747f95a1b\\transformed\\startup-runtime-1.1.1\\res\\values\\values.xml", "from": {"startLines": "-1", "startColumns": "-1", "startOffsets": "-1"}, "to": {"startLines": "330", "startColumns": "4", "startOffsets": "21193", "endColumns": "82", "endOffsets": "21271"}}]}]}