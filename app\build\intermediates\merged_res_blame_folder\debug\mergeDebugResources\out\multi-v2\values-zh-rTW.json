{"logs": [{"outputFile": "com.wendy.face.app-mergeDebugResources-56:/values-zh-rTW/values-zh-rTW.xml", "map": [{"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\1722e60b3e06639d9e378fb2f4330d38\\transformed\\ui-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,182,258,345,436,514,588,665,743,818,891,966,1034,1107,1179,1250,1324,1392", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,72,71,70,73,67,115", "endOffsets": "177,253,340,431,509,583,660,738,813,886,961,1029,1102,1174,1245,1319,1387,1503"}, "to": {"startLines": "36,37,56,57,58,59,60,118,119,120,121,123,124,125,126,128,129,130", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3332,3409,5304,5391,5482,5560,5634,11243,11321,11396,11469,11622,11690,11763,11835,12007,12081,12149", "endColumns": "76,75,86,90,77,73,76,77,74,72,74,67,72,71,70,73,67,115", "endOffsets": "3404,3480,5386,5477,5555,5629,5706,11316,11391,11464,11539,11685,11758,11830,11901,12076,12144,12260"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\b463a313bba75631a42e21f95a59b107\\transformed\\play-services-base-18.1.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20", "startColumns": "0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0", "startOffsets": "197,294,418,529,627,730,842,938,1029,1135,1232,1357,1468,1566,1670,1722,1775", "endColumns": "96,123,110,97,102,111,95,90,105,96,124,110,97,103,51,52,69", "endOffsets": "293,417,528,626,729,841,937,1028,1134,1231,1356,1467,1565,1669,1721,1774,1844"}, "to": {"startLines": "38,39,40,41,42,43,44,45,47,48,49,50,51,52,53,54,55", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "3485,3586,3714,3829,3931,4038,4154,4254,4452,4562,4663,4792,4907,5009,5117,5173,5230", "endColumns": "100,127,114,101,106,115,99,94,109,100,128,114,101,107,55,56,73", "endOffsets": "3581,3709,3824,3926,4033,4149,4249,4344,4557,4658,4787,4902,5004,5112,5168,5225,5299"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\bd665940b510ccfb87ca6845c9679e75\\transformed\\play-services-basement-18.1.0\\res\\values-zh-rTW\\values.xml", "from": {"startLines": "4", "startColumns": "0", "startOffsets": "199", "endColumns": "98", "endOffsets": "297"}, "to": {"startLines": "46", "startColumns": "4", "startOffsets": "4349", "endColumns": "102", "endOffsets": "4447"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\7982a989e315c5ea24cc186b05f9e71f\\transformed\\appcompat-1.1.0\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,833,925,1018,1115,1211,1307,1401,1497,1589,1681,1773,1850,1946,2041,2136,2233,2329,2427,2577,2671", "endColumns": "94,92,99,81,96,107,76,75,91,92,96,95,95,93,95,91,91,91,76,95,94,94,96,95,97,149,93,77", "endOffsets": "195,288,388,470,567,675,752,828,920,1013,1110,1206,1302,1396,1492,1584,1676,1768,1845,1941,2036,2131,2228,2324,2422,2572,2666,2744"}, "to": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,122", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "105,200,293,393,475,572,680,757,833,925,1018,1115,1211,1307,1401,1497,1589,1681,1773,1850,1946,2041,2136,2233,2329,2427,2577,11544", "endColumns": "94,92,99,81,96,107,76,75,91,92,96,95,95,93,95,91,91,91,76,95,94,94,96,95,97,149,93,77", "endOffsets": "195,288,388,470,567,675,752,828,920,1013,1110,1206,1302,1396,1492,1584,1676,1768,1845,1941,2036,2131,2228,2324,2422,2572,2666,11617"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\f05ef868ca0f438ebd98b29812d4e3a6\\transformed\\material3-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "55,159,262,366,468,560,648,752,857,962,1078,1160,1256,1340,1428,1533,1646,1747,1856,1963,2071,2188,2293,2394,2498,2603,2688,2783,2888,2997,3087,3187,3285,3396,3512,3612,3703,3777,3867,3956,4048,4131,4213,4302,4382,4464,4561,4655,4748,4841,4925,5022,5118,5213,5321,5401,5495", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "154,257,361,463,555,643,747,852,957,1073,1155,1251,1335,1423,1528,1641,1742,1851,1958,2066,2183,2288,2389,2493,2598,2683,2778,2883,2992,3082,3182,3280,3391,3507,3607,3698,3772,3862,3951,4043,4126,4208,4297,4377,4459,4556,4650,4743,4836,4920,5017,5113,5208,5316,5396,5490,5582"}, "to": {"startLines": "61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,77,78,79,80,81,82,83,84,85,86,87,88,89,90,91,92,93,94,95,96,97,98,99,100,101,102,103,104,105,106,107,108,109,110,111,112,113,114,115,116,117", "startColumns": "4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4", "startOffsets": "5711,5815,5918,6022,6124,6216,6304,6408,6513,6618,6734,6816,6912,6996,7084,7189,7302,7403,7512,7619,7727,7844,7949,8050,8154,8259,8344,8439,8544,8653,8743,8843,8941,9052,9168,9268,9359,9433,9523,9612,9704,9787,9869,9958,10038,10120,10217,10311,10404,10497,10581,10678,10774,10869,10977,11057,11151", "endColumns": "103,102,103,101,91,87,103,104,104,115,81,95,83,87,104,112,100,108,106,107,116,104,100,103,104,84,94,104,108,89,99,97,110,115,99,90,73,89,88,91,82,81,88,79,81,96,93,92,92,83,96,95,94,107,79,93,91", "endOffsets": "5810,5913,6017,6119,6211,6299,6403,6508,6613,6729,6811,6907,6991,7079,7184,7297,7398,7507,7614,7722,7839,7944,8045,8149,8254,8339,8434,8539,8648,8738,8838,8936,9047,9163,9263,9354,9428,9518,9607,9699,9782,9864,9953,10033,10115,10212,10306,10399,10492,10576,10673,10769,10864,10972,11052,11146,11238"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\afde12078f7f3fef585f13cd9d4f1674\\transformed\\core-1.13.1\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3,4,5,6,7,8,9", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "55,147,246,340,434,527,620,716", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "142,241,335,429,522,615,711,812"}, "to": {"startLines": "29,30,31,32,33,34,35,127", "startColumns": "4,4,4,4,4,4,4,4", "startOffsets": "2671,2763,2862,2956,3050,3143,3236,11906", "endColumns": "91,98,93,93,92,92,95,100", "endOffsets": "2758,2857,2951,3045,3138,3231,3327,12002"}}, {"source": "C:\\Users\\<USER>\\.gradle\\caches\\8.11.1\\transforms\\aaefdcdcf409a8deae73ec68228806f7\\transformed\\foundation-release\\res\\values-zh-rTW\\values-zh-rTW.xml", "from": {"startLines": "2,3", "startColumns": "4,4", "startOffsets": "55,138", "endColumns": "82,78", "endOffsets": "133,212"}, "to": {"startLines": "131,132", "startColumns": "4,4", "startOffsets": "12265,12348", "endColumns": "82,78", "endOffsets": "12343,12422"}}]}]}